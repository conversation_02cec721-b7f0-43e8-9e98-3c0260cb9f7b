<script lang="ts" setup>
name: 'SchemeIndex';
import { ref, reactive, onMounted, useAttrs, inject, computed, provide } from 'vue';
import { collapse, Button, message, Table, affix, Modal, Upload } from 'ant-design-vue';
import { schemeApi, miceBidManOrderListApi, fileApi } from '@haierbusiness-front/apis';
import { useRoute, useRouter } from 'vue-router';
import { QuestionCircleOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { getDealTime, routerParam, resolveParam } from '@haierbusiness-front/utils';
import { tempSchemeRes, tempDemandRes } from './data';
import dayjs, { Dayjs } from 'dayjs';
import {
  MiceTypeConstantO,
  SelCollapseS,
  MiceDetail,
  hotelLevelAllConstant,
  PlaceUsageTimeTypeConstant,
  MaterialTypeConstant,
  UsagePurposeTypeConstant,
  TableTypeConstant,
  RoomTypeConstant,
  BreakfastTypeConstant,
  HotelDinnerTypeConstant,
  CateringTypeConstant,
  CateringTimeTypeConstant,
  HaveDrinksTypeConstant,
  CarUsageTypeConstant,
  CarBrandTypeConstant,
  SeatTypeConstant,
  UsageTimeTypeConstant,
  AttendantTypeConstant,
  SchemeLedSource,
  MiceItemConstant,
  ProcessNode,
  MerchantType,
  MiceSchemePushStrategy,
} from '@haierbusiness-front/common-libs';
import miceHeader from '../../miceHeader.vue';
const loading = ref(false);
const checked = ref(true);
const hideBtn = ref<string>('');
const heightTable = ref(400);
const widthTable = ref(1500);
const miceDetail = reactive<MiceDetail>({
  miceName: '',
  mainCode: 'RC2025011645636',
});
const isCloseLastTab = inject<Ref<boolean>>('isCloseLastTab');

const showMoney = ref(true);
const columnsSum = ref([]);
const dataSum = ref([]);
const columns = ref([]);
const schemeSum = ref([]);
const schemeSumInitiate = ref(inject('schemeSumInitiate'));

const arrConfirmView = ref([
  'COST_APPROVAL',
  'MICE_PENDING',
  'MICE_EXECUTION',
  'MICE_COMPLETED',
  'BILL_CONFIRM',
  'BILL_APPROVAL',
  'BILL_RE_APPROVAL',
  'PAYMENT_CONFIRM',
  'PLATFORM_INVOICE_ENTRY',
  'VENDOR_INVOICE_ENTRY',
  'INVOICE_CONFIRM',
  'PLATFORM_REFUND_RECEIPT_UPLOAD',
  'REFUND_CONFIRM',
  'PLATFORM_PAY_RECEIPT_UPLOAD',
  'PLATFORM_INVOICE_CONFIRM',
  'SETTLEMENT_PENDING',
  'SETTLEMENT_RECORDED',
  'END',
]);
const data = ref([]);
const bidColumn = ref([
  { title: '服务商类型', dataIndex: 'type', key: 'type' },
  { title: '标的数量', dataIndex: 'num', key: 'num' },
  { title: '竞价推送策略', dataIndex: 'scheme', key: 'scheme' },
]);
const bidedColumn = ref([
  {
    title: '服务商类型',
    dataIndex: 'type',
    key: 'type',
    customCell: (record, rowIndex, column) => {
      return { rowSpan: record.rowSpan };
    },
  },
  {
    title: '标的数量',
    dataIndex: 'num',
    key: 'num',
    customCell: (record, rowIndex, column) => {
      return { rowSpan: record.rowSpan };
    },
  },
  { title: '方案名称', dataIndex: 'schemeName', key: 'schemeName' },
  { title: '参与服务商', dataIndex: 'scheme', key: 'scheme' },
  {
    title: '竞价截止时间',
    dataIndex: 'biddingDeadline',
    key: 'biddingDeadline',
    customCell: (record, rowIndex, column) => {
      return { rowSpan: record.rowSpan };
    },
  },
]);
const bidData = ref([]);
const bidDataInitial = ref([]);
const bidedData = ref([]);
const attachmentList = ref<array>([]);
const attachmentListPush = ref<array>([]);

// 记录初始值，用于判断是否修改
const initialPublishDate = ref<Dayjs>();
const initialPublishHour = ref<string>('');
// 证明材料文件
const proofFile = ref<File | null>(null);
// 计算属性：判断是否修改了截止时间
const isPublishTimeChanged = computed(() => {
  return (
    dayjs(publishDate.value).format('YYYY-MM-DD') != dayjs(initialPublishDate.value).format('YYYY-MM-DD') ||
    publishHour.value != initialPublishHour.value
  );
});

const isPublishSchemeChanged = computed(() => {
  let ifshow = false;
  for (let i = 0; i < bidData.value.length; i++) {
    if (bidData.value[i].scheme != bidDataInitial.value[i].scheme) ifshow = true;
  }
  return ifshow;
});
const uploadLoading = ref<boolean>(false);
const isLt50M = ref<boolean>(true);

const beforeUpload = (file) => {
  isLt50M.value = file.size / 1024 / 1024 < 50;

  if (!isLt50M.value) {
    message.error('文件最大不超过50M！');
    return Upload.LIST_IGNORE;
  }

  return isLt50M.value;
};

const baseUrl = import.meta.env.VITE_BUSINESS_URL;
const uploadRequest = (options: any) => {
  uploadLoading.value = true;
  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;
      options.onProgress(100);
      options.onSuccess(it, options.file); //这里必须加上这个，不然会一直显示一个正在上传中的框框
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};
const handleRemove = (file: any) => {
  const index = attachmentList.value.findIndex((item) => item.uid === file.uid);
  if (index > -1) {
    attachmentList.value.splice(index, 1);
  }
};

const handleRemovePush = (file: any) => {
  const index = attachmentListPush.value.findIndex((item) => item.uid === file.uid);
  if (index > -1) {
    attachmentListPush.value.splice(index, 1);
  }
};
const dealTableData = (tempDatas, demandTempDatas, resFileds, titleList, title, time) => {
  if (time === undefined)
    data.value.push({
      key: '2',
      leftTitle: '',
      secondTitle: '',
      demand: (time || '') + title + '需求',
      type: 'firstTitle',
      filed1: '',
      filed2: '',
      filde3: '',
    });
  let colArr = [];
  // 循环对应字段数组
  for (let i = 0; i < tempDatas.length; i++) {
    // // 循环出每个酒店的行数据
    for (let j = 0; j < resFileds.length; j++) {
      let col = {};
      // 循环出每列共多少个方案字段
      for (let m = 0; m < schemeSum.value.length; m++) {
        col = {
          ...col,
          ['filed' + (m + 1)]: dealData(
            resFileds[j],
            tempDatas[i][resFileds[j] + (m + 1)],
            schemeSum.value[m].hotels,
            tempDatas[i],
            m + 1,
            title,
          ),
        };
      }
      colArr.push(col);
    }
  }
  let tempDataArr = [];
  // 以酒店为单位添加行数据
  for (let i = 0; i < tempDatas.length; i++) {
    let tempArr = [];
    for (let j = 0; j < titleList.length; j++) {
      let randomKey = String(Math.random()).split('.')[1];
      let rowSpan = titleList.length;
      if (!showMoney.value) {
        if (['餐饮', '用车', '服务人员', '拓展活动', '保险', '布展物料', '礼品'].includes(title))
          rowSpan = titleList.length - 2;
        else if (['会场'].includes(title)) rowSpan = titleList.length - 4;
        else if (['住宿', '其它', '全单服务费'].includes(title)) rowSpan = titleList.length - 1;
      }
      tempArr.push({
        key: randomKey,
        leftTitle: title + (!['全单服务费', '住宿差异'].includes(title) ? i + 1 : ''),
        leftTitleR: j === 0 ? rowSpan : 0,
        demandR: j === 0 ? rowSpan : 0,
        demand: demandTempDatas?.[i] || undefined,
        secondTitle: titleList[j] === '路线' && demandTempDatas?.[0]?.[0]?.value === '包车' ? '路线概述' : titleList[j],
      });
    }
    tempDataArr = tempDataArr.concat(tempArr);
  }
  // 酒店行数组和字段数据合并
  tempDataArr = tempDataArr.map((item, index) => {
    return { ...item, ...colArr[index] };
  });

  return tempDataArr;
};
const extractNumbers = (str) => {
  if (['NaN', NaN, 'undefined', undefined, 'null', null].includes(str)) return 0;
  const pattern = /\d+/g;
  const matches = str.match(pattern);
  return matches ? Number(matches.map(Number).join('')) : 0;
};
const dealSumRow = (row, type, data) => {
  if (type) {
    let unit = '';
    if (type === '住宿') unit = '间夜';
    else if (['餐饮'].includes(type)) unit = '人';
    else if (type === '用车') unit = '辆';
    let ifHasH = false;
    data.forEach((item) => {
      if (item.hotelName == row[0]) {
        ifHasH = true;
        let ifHasI = false;
        item.list.forEach((item1) => {
          if (['服务人员', '拓展活动', '保险'].includes(type)) {
            if (item.hotelName == row[0] && item1[0] && row[1]) {
              ifHasI = true;
              item1[0] =
                extractNumbers(item1[0]) + extractNumbers(row[1]) + item1[0].split(extractNumbers(item1[0]) + '')[1];
              if (item1[1] && row[2]) {
                item1[1] = '=' + (extractNumbers(item1[1]) + extractNumbers(row[2])) + '元';
              }
            }
          } else if (type == '会场') {
            if (item.hotelName == row[0] && item1.length == 3) {
              ifHasI = true;
              if (item1[0] && row[1]) {
                item1[0] =
                  item1[0].split(extractNumbers(item1[0]) + '')[0] +
                  (extractNumbers(item1[0]) + extractNumbers(row[1])) +
                  item1[0].split(extractNumbers(item1[0]) + '')[1];
              }
            } else {
              if (item1[0] == row[1]) ifHasI = true;
            }
          } else {
            if (item1[0] == row[1] && item1?.[1] && row?.[2]) {
              ifHasI = true;
              item1[1] = extractNumbers(item1[1]) + extractNumbers(row[2]) + unit;
              if (item1[2] && row[3]) {
                item1[2] = '=' + (extractNumbers(item1[2]) + extractNumbers(row[3])) + '元';
              }
            }
          }
        });
        if (!ifHasI) {
          item.list.push(row.slice(1));
        }
      }
    });
    if (!ifHasH)
      data.push({
        hotelName: row[0],
        list: [row.slice(1)],
      });
  }
};
const dealSumData = () => {
  data.value.push({
    key: '2',
    leftTitle: '',
    secondTitle: '',
    demand: '合计',
    type: 'firstTitle',
  });
  let arr = [];
  dataSum.value.forEach((dataS, dataSIndex) => {
    arr.push({
      subject: dataS.subject,
    });
    for (let key in dataS) {
      if (dataS.subject == '全单服务费' && key == 'demand') arr[dataSIndex][key] = [{ hotelName: '/' }];
      else if (Array.isArray(dataS[key])) {
        arr[dataSIndex][key] = [];
        dataS[key].forEach((row) => {
          dealSumRow(row, dataS.subject, arr[dataSIndex][key]);
        });
      } else {
        arr[dataSIndex][key] = dataS[key];
      }
    }
  });
  arr.forEach((item) => {
    let randomKey = String(Math.random()).split('.')[1];
    if (
      item?.demand?.length > 0 ||
      ['合计'].includes(item.subject) ||
      (['全单服务费'].includes(item.subject) && showMoney.value)
    )
      data.value.push({
        key: 'sum' + randomKey,
        leftTitle: item.subject,
        leftTitleC: 2,
        secondTitle: '',
        type: '',
        ...item,
      });
  });
  setTimeout(() => {
    let originHeight = 800;
    if ($attrs.orderSource === 'user') originHeight = 770;
    let element =
      (['SCHEME_CONFIRM'].includes(miceDetail.processNode) ? 60 : 0) +
      originHeight -
      document.getElementsByClassName('ant-layout-sider')?.offsetHeight;
    // widthTable.value = 360 + 300 * schemeSum.length;
    heightTable.value = element || '80%';
    window.addEventListener('resize', function () {
      let element =
        (['SCHEME_CONFIRM'].includes(miceDetail.processNode) ? 60 : 0) +
        originHeight -
        document.getElementsByClassName('ant-layout-sider')?.offsetHeight;
      heightTable.value = element || '80%';
    });
  }, 100);
};
const router = useRouter();
let $attrs = useAttrs();
const approveCode = ref<string>(''); // 审批流Code
const businessProcess = import.meta.env.VITE_BUSINESS_PROCESS_URL;
const approvalModalShow = ref(false);
const subLoading = ref(false);
const saveScheme = async () => {
  try {
    subLoading.value = true;
    loading.value = true;
    let list = [];
    let res;
    if (route.path === '/bidman/scheme/index') {
      schemeSum.value.forEach((item) => {
        list.push({
          schemeCombinationId: item.id,
          isExclude: item.isExclude,
          excludeRemarks: item.excludeRemarks,
        });
      });
      res = await schemeApi.schemeExamine({
        miceId: route.query?.miceId || JSON.parse(route.query.record).miceId,
        excludeSchemes: list,
      });
      approveCode.value = res.data.processCode;
      approvalModalShow.value = true;
      return;
    } else if (route.path === '/bidman/scheme/confirm') {
      let num = 0;
      schemeSum.value.forEach((item) => {
        let isSelectedTemp = item.isSelected;
        if (configCheck('SCHEME_CONFIRM', 'schemeConfirmSelectedTypeConfigDefine') == '0')
          isSelectedTemp = !item.isExcludeTemp;
        if (item.isSelected) num++;
        list.push({
          schemeCombinationId: item.id,
          isSelected: isSelectedTemp,
          unselectRemarks: item.unselectRemarks,
        });
      });
      res = await schemeApi.userConfirm({
        miceId: route.query?.miceId || JSON.parse(route.query.record).miceId,
        selectedSchemes: list,
      });
    } else if (route.path === '/bidman/bid/index') {
      // 发布竞价
      const currentDate = new Date();
      const year = currentDate.getFullYear();
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const day = String(currentDate.getDate()).padStart(2, '0');
      const hour = String(currentDate.getHours()).padStart(2, '0');
      currentHour.value = hour;
      const minute = String(currentDate.getMinutes()).padStart(2, '0');
      const second = String(currentDate.getSeconds()).padStart(2, '0');
      let numLimit = currentDate.getHours() + 2;
      let dayLimit = day;
      if (configCheck('BID_PUSH', 'bidPushEndTimeConfigDefine')) {
        numLimit =
          configCheck('BID_PUSH', 'bidPushEndTimeConfigDefine').split(',')[1]?.split(':')[0] ||
          currentDate.getHours() + 2;
        dayLimit = configCheck('BID_PUSH', 'bidPushEndTimeConfigDefine').split(',')[0];
      }
      currentPublishDate.value = dayjs(`${year}-${month}-${Number(day)}`);
      publishDate.value = dayjs(`${year}-${month}-${Number(day) + Number(dayLimit)}`);
      publishHour.value = String(currentDate.getHours() === '23' ? '00' : numLimit).padStart(2, '0');
      currentPublishHour.value = String(
        currentDate.getHours() === '23' ? '00' : Number(currentDate.getHours()) + 2,
      ).padStart(2, '0');
      initialPublishDate.value = publishDate.value;
      initialPublishHour.value = publishHour.value;
      res = await schemeApi.pushList({
        miceId: route.query?.miceId || JSON.parse(route.query.record).miceId,
      });
      bidData.value = [];
      res.forEach((item) => {
        bidData.value.push({
          merchantType: item.merchantType,
          type: MerchantType.ofType(item.merchantType)?.desc,
          num: item.bidNum,
          scheme: item.merchantType == 2 ? 3 : '/',
        });
      });
      bidDataInitial.value = JSON.parse(JSON.stringify(bidData.value));
      showBidPush.value = true;
    }
    if (res?.success) {
      message.success('提交成功！');
      open.value = false;
      let path = '/bidman/orderList/index';
      if ($attrs.orderSource === 'user') {
        path = '';
        const businessMiceBid = import.meta.env.VITE_BUSINESS_INDEX_URL + '#';
        // 跳转需求确认页面
        const url = businessMiceBid + '/card-order/miceOrder';
        window.location.href = url;
        return;
      }
      router.push({ path: path, query: { status: '0' } });
      // 关闭当前页签
      isCloseLastTab.value = true;
    }
  } finally {
    subLoading.value = false;
    loading.value = false;
  }
};
const open = ref(false);
const openExcludeTemp = ref(false);
const reason = ref('');
const currentSchemeNum = ref(0);
const differentList = ref([]);
const advantageList = ref([]);
const differentCell = (text, record, index, column) => {
  let demandStr = '';
  if (!checked.value) return;
  if (text === '-') return;
  let resCless = '';
  // if (record.secondTitle === '星级') {
  //   if (record.demand && text != record.demand[1]?.value) resCless = 'different-cell';
  // } else
  if (record.secondTitle === '当日总人数') {
    if (record.demand && text != record.demand[0]?.value) {
      demandStr = record.demand[0]?.name + '：' + record.demand[0]?.value;
      resCless = 'different-cell';
    }
  } else if (record.secondTitle === '方案合计') {
    if (record.demand) {
      if (
        !text.includes(record.demand[1]?.value.split('+')[0]) &&
        !text.includes(record.demand[1]?.value.split('+')[1])
      ) {
        demandStr = record.demand[1]?.name + '：' + record.demand[1]?.value;
        resCless = 'different-cell';
      }
    }
  } else if (record.secondTitle === '大床房间夜数') {
    if (record.demand && text != record.demand[2]?.value) {
      demandStr = record.demand[2]?.name + '：' + record.demand[2]?.value;
      resCless = 'different-cell';
    }
  } else if (record.secondTitle === '双床房间夜数') {
    if (record.demand && text != record.demand[3]?.value) {
      demandStr = record.demand[3]?.name + '：' + record.demand[3]?.value;
      resCless = 'different-cell';
    }
  } else if (record.secondTitle === '套房间夜') {
    if (record.demand && text != record.demand[4]?.value) {
      demandStr = record.demand[4]?.name + '：' + record.demand[4]?.value;
      resCless = 'different-cell';
    }
  } else if (record.secondTitle === '面积') {
    if (record.demand && text != record.demand[4]?.value) {
      demandStr = record.demand[4]?.name + '：' + record.demand[4]?.value;
      resCless = 'different-cell';
    }
  } else if (record.secondTitle === '灯下层高') {
    if (record.demand && text != record.demand[5]?.value) {
      demandStr = record.demand[5]?.name + '：' + record.demand[5]?.value;
      resCless = 'different-cell';
    }
  } else if (record.secondTitle === '用餐单价') {
    if (record.demand && text != record.demand[4]?.value) {
      demandStr = record.demand[4]?.name + '：' + record.demand[4]?.value;
      resCless = 'different-cell';
    }
  }
  //  else if (record.secondTitle === '礼品描述') {
  //   if (record.demand && text != record.demand[0]?.value) {
  // demandStr=
  // resCless = 'different-cell'};
  // }
  else if (record.secondTitle === '数量' && record.leftTitle.includes('礼品')) {
    if (record.demand && text != record.demand[1]?.value) {
      demandStr = record.demand[1]?.name + '：' + record.demand[1]?.value;
      resCless = 'different-cell';
    }
  }
  // else if (record.secondTitle === '费用标准' && record.leftTitle.includes('礼品')) {
  //    //if (record.demand && text != record.demand[2]?.value) resCless = 'different-cell';
  //} else if (record.secondTitle === '送达日期' && record.leftTitle.includes('礼品')) {
  //    //if (record.demand && text != record.demand[3]?.value) resCless = 'different-cell';
  // }
  if (resCless) {
    let str = '需求' + demandStr + '；' + record.leftTitle + record.secondTitle + '：' + text + '；';
    if (!differentList.value[column.sort - 3]?.includes(str)) differentList.value[column.sort - 3]?.push(str);
  }
  return resCless;
};
const saveSchemeConfirm = () => {
  let num = 0;
  let tip = '选择';
  let numLimit = 1;
  schemeSum.value.forEach((item) => {
    if (configCheck('SCHEME_CONFIRM', 'schemeConfirmSelectedTypeConfigDefine') == '0') {
      if (!item.isExcludeTemp) num++;
    } else {
      if (item.isSelected) num++;
    }
  });
  if (configCheck('SCHEME_CONFIRM', 'schemeConfirmSelectedTypeConfigDefine') == '0') {
    tip = '保留';
    if (configCheck('SCHEME_CONFIRM', 'schemeConfirmSelectedMinimumConfigDefine')) {
      numLimit = configCheck('SCHEME_CONFIRM', 'schemeConfirmSelectedMinimumConfigDefine');
      if (schemeSum.value.length < numLimit) {
        numLimit = schemeSum.value.length;
        if (num < numLimit) {
          message.error('方案总数低于配置数量，请保留全部方案');
          return;
        }
      }
    }
  } else {
    if (configCheck('SCHEME_CONFIRM', 'schemeConfirmSelectedMinimumConfigDefine')) {
      numLimit = configCheck('SCHEME_CONFIRM', 'schemeConfirmSelectedMinimumConfigDefine');
      if (schemeSum.value.length < numLimit) {
        numLimit = schemeSum.value.length;
        if (num < numLimit) {
          message.error('方案总数低于配置数量，请全选方案');
          return;
        }
      }
    }
  }
  if (num >= numLimit) {
    Modal.confirm({
      title: '方案确认',
      content: '是否确认方案，确认后将进行竞价。',
      onOk() {
        saveScheme();
      },
      onCancel() {
        console.log('Cancel');
      },
      class: 'test',
    });
  } else {
    message.error('请至少' + tip + numLimit + '套方案！');
    return;
  }
};
const computedMethod = (num) => {
  if (!num) {
    return;
  }

  const list = MiceItemConstant.toArray().map((item) => item.code);
  // method
  const array = [];
  list.map((item) => {
    if ((num & item) != 0) {
      array.push(item);
    }
  });
  return array;
};
// const MoneySum = ref([]);
const dealData = (type, value, hotels, data, num, title) => {
  let sum = '';
  let index = num || '';
  if (['schemeTotalPriceSt'].includes(type)) {
    sum = `${RoomTypeConstant.ofType(data['roomType' + index])?.desc}${data['schemeUnitPrice' + index]}元*${
      data['schemeRoomNum' + index]
    }人=${data['schemeUnitPrice' + index] * data['schemeRoomNum' + index]}元`;
  } else if (['schemeTotalPriceC'].includes(type)) {
    sum = `${data['schemeUnitPrice' + index]}元*${data['schemePersonNum' + index]}人=${
      data['schemeUnitPrice' + index] * data['schemePersonNum' + index]
    }元`;
  } else if (['schemeTotalPriceP'].includes(type)) {
    sum = `${data['guildhall' + index]}${data['schemeUnitPlacePrice' + index]}元${
      data['hasTea' + index] ? '+茶歇' + data['schemeUnitTeaPrice' + index] + '元*' : ''
    }${data['hasTea' + index] ? data['schemePersonNum' + index] + '人' : ''}${
      data['hasLed' + index] ? '+LED:' + data['schemeUnitLedPrice' + index] + '元*' : ''
    }${data['hasLed' + index] ? data['schemeLedNum' + index] + '个' : ''}=${
      data['schemeUnitPlacePrice' + index] +
      (data['hasTea' + index] ? data['schemeUnitTeaPrice' + index] * data['schemePersonNum' + index] : 0) +
      (data['hasLed' + index] ? data['schemeUnitLedPrice' + index] * data['schemeLedNum' + index] : 0)
    }元`;
  } else if (['schemeTotalPriceV'].includes(type)) {
    sum = `${data['seats' + index]}座${data['brand' + index]}${data['schemeUnitPrice' + index]}辆*${
      data['schemeVehicleNum' + index]
    }元=${data['schemeUnitPrice' + index] * data['schemeVehicleNum' + index]}元`;
  } else if (['schemeTotalPriceA'].includes(type)) {
    sum = `${AttendantTypeConstant.ofType(data['type' + index])?.desc}${data['schemeUnitPrice' + index]}元*${
      data['schemePersonNum' + index]
    }人=${data['schemeUnitPrice' + index] * data['schemePersonNum' + index]}元`;
  } else if (['schemeTotalPriceI'].includes(type)) {
    sum = `${data['insuranceName' + index]}${data['schemeUnitPrice' + index]}元*${data['schemePersonNum' + index]}人=${
      data['schemeUnitPrice' + index] * data['schemePersonNum' + index]
    }元`;
  } else if (['schemeUnitPriceM'].includes(type)) {
    sum = `${MaterialTypeConstant.ofType(data['type' + index])?.desc}${data['schemeUnitPrice' + index]}元*${
      data['schemeMaterialNum' + index]
    }个=${data['schemeUnitPrice' + index] * data['schemeMaterialNum' + index]}元`;
  } else if (['schemeTotalPriceO'].includes(type)) {
    sum = `${data['demandTotalPrice' + index]}元`;
  } else if (['schemeTotalPriceAc'].includes(type)) {
    sum = `拓展活动${data['schemeUnitPrice' + index]}元*${data['schemePersonNum' + index]}人=${
      data['schemeUnitPrice' + index] * data['schemePersonNum' + index]
    }`;
  } else if (['schemeTotalPriceM'].includes(type)) {
    sum = `${data['schemeUnitPrice' + index]}元`;
  } else if (['schemeTotalPriceP'].includes(type)) {
    sum = `${data['schemeUnitPrice' + index]}元`;
  } else if (['schemeTotalPriceS'].includes(type)) {
    sum = `${data['schemeServiceFeeReal' + index]}元`;
  }
  if (sum) {
    let res = sum?.replaceAll('undefined', '-')?.replaceAll('null', '-')?.replaceAll('NaN', '-');
    if (showMoney.value && res.includes('-')) return '-';
    if (!num && res.includes('=')) res = sum.split('=')[1];
    return res;
  }
  if ([undefined, null, '', 'null'].includes(value)) return '-';
  if (['level'].includes(type)) {
    return hotelLevelAllConstant.ofType(value)?.desc || '-';
  } else if (['roomType'].includes(type)) {
    return (
      RoomTypeConstant.ofType(value)?.desc +
      (data && num != 'differences'
        ? '(' + BreakfastTypeConstant.ofType(data['breakfastType' + (title ? num : '')])?.desc + ')'
        : '')
    );
  } else if (['demandRoomType'].includes(type)) {
    let str = '';
    value.split(',').forEach((item) => {
      let num = 0;
      if (item == 1) num = data['schemeOneRooms' + index] || 0;
      else if (item == 2) num = data['schemeTwoRooms' + index] || 0;
      else if (item == 3) num = data['schemeSuiteRooms' + index] || 0;
      if (num > 0) str += RoomTypeConstant.ofType(Number(item))?.desc;

      //  + ('*' + num + ' ');
    });
    return str;
  } else if (['isInsideHotel'].includes(type)) {
    return HotelDinnerTypeConstant.ofType(Number(value))?.desc || '-';
  } else if (['cateringType'].includes(type)) {
    return CateringTypeConstant.ofType(value)?.desc || '-';
  } else if (['cateringTime'].includes(type)) {
    return CateringTimeTypeConstant.ofType(value)?.desc || '-';
  } else if (['usageTime'].includes(type)) {
    return UsageTimeTypeConstant.ofType(value)?.desc || PlaceUsageTimeTypeConstant.ofType(value)?.desc;
  } else if (['usagePurpose'].includes(type)) {
    return UsagePurposeTypeConstant.ofType(value)?.desc || '-';
  } else if (['tableType'].includes(type)) {
    return TableTypeConstant.ofType(value)?.desc || '-';
  } else if (['schemeLedSource'].includes(type)) {
    return SchemeLedSource.ofType(Number(value))?.desc;
  } else if (['usageType'].includes(type)) {
    return CarUsageTypeConstant.ofType(value)?.desc;
  } else if (['personNum', 'schemePersonNum'].includes(type)) {
    if (title === '礼品') {
      return value + (data['unit' + (num || '')] || '');
    }
    return value + '人';
  } else if (['roomNum', 'schemeRoomNum', 'schemeOneRooms', 'schemeTwoRooms', 'schemeSuiteRooms'].includes(type)) {
    return value + '间夜';
  } else if (['type'].includes(type)) {
    if (title === '布展物料') return MaterialTypeConstant.ofType(value)?.desc;
    return AttendantTypeConstant.ofType(value)?.desc;
  } else if (['seats'].includes(type)) {
    return value + '座';
  } else if (['num'].includes(type)) {
    if (['布展物料', '其它'].includes(title)) return value + (data['unit' + (num || '')] || '');
    return value + '';
  } else if (['vehicleNum'].includes(type)) {
    return value + '辆';
  } else if (['route'].includes(type)) {
    if (data['usageType' + (num || '')] == 0) return value.replaceAll(',', '-');
    return value;
  } else if (
    [
      'schemeUnitPlacePrice',
      'schemeUnitLedPrice',
      'demandTotalPrice',
      'unitPrice',
      'schemeUnitPrice',
      'teaEachTotalPrice',
      'demandUnitPrice',
      'schemeServiceFeeReal',
      'schemeTotalPrice',
      'schemeUnitTeaPrice',
    ].includes(type)
  ) {
    return value + '元';
  } else if (['serviceFeeRate'].includes(type)) {
    return value + '%';
  } else if (['schemeMaterialNum'].includes(type)) {
    return value + (data['unit' + (num || '')] || '');
  } else if (['area'].includes(type)) {
    return value + 'm²';
  } else if (['paths'].includes(type)) {
    if (value.length > 0) {
      let str = '';
      let document = {};
      value.forEach((item, index) => {
        try {
          document = JSON.parse(item);
        } catch (error) {
          console.log(error);
        }

        str += `<a target='_blank' href='${document.url}'>${
          document.name
        }</a><span style='margin-right: 10px;color: #86909c' >${index === value.length - 1 ? '' : ','}</span>`;
      });
      return str;
    }
    return '-';
  } else if (['underLightFloor'].includes(type)) {
    return value + 'm';
  } else if (['isIncludeDrinks'].includes(type)) {
    return value ? '是' : '否' || '-';
  } else if (['miceDemandHotelId', 'miceSchemeHotelId'].includes(type)) {
    if (title === '餐饮' && !data['isInsideHotel' + num]) return '非酒店提供';
    // if(data.isInsideHotel)
    let hotelName = '-';
    hotels.forEach((item) => {
      if (item[type] === value || item.id === value) hotelName = item.hotelName || item.centerMarker;
    });
    return hotelName || '-';
    // return hotelLevelAllConstant.ofType(value)?.desc || '-';
  } else return value;
};
const getNumber = (str: string) => {
  if (str)
    return str
      .split('')
      .filter((item) => !isNaN(item))
      .join('');
};
// 复制文本到剪贴板
const getCopy = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    message.success('复制成功！');
  } catch (err) {
    message.success('复制失败');
  }
};
const hotelIdList = ref([]);
const route = useRoute();
const getSchemeName = (column) => {
  let currentScheme = schemeSum.value[column.sort - 3];
  let res = [];

  if (currentScheme.isOverBudget) res.push({ name: '超', detail: '超预算', color: '#f50' });
  if (
    currentScheme.isSelected &&
    route.query.type != 'c' &&
    configCheck('SCHEME_CONFIRM', 'schemeConfirmSelectedTypeConfigDefine') == '1'
  )
    res.push({ name: '选', detail: '用户选择', color: '#2db7f5' });
  if (currentScheme.isExclude && route.query.type != 'c')
    res.push({ name: '排', detail: '顾问排除，排除原因：' + currentScheme.excludeRemarks, color: '#2db7f5' });
  else if (
    !currentScheme.isSelected &&
    !['SCHEME_SUBMIT', 'SCHEME_APPROVAL', 'SCHEME_RE_APPROVAL', 'SCHEME_CONFIRM'].includes(miceDetail.processNode) &&
    route.query.type != 'c' &&
    configCheck('SCHEME_CONFIRM', 'schemeConfirmSelectedTypeConfigDefine') != '1'
  )
    res.push({
      name: '否',
      detail: '用户排除' + (currentScheme.unselectRemarks ? '，排除原因：' + currentScheme.unselectRemarks : ''),
      color: '#2db7f5',
    });
  if (currentScheme.isTarget && route.query.type != 'c') res.push({ name: '标', detail: '标的方案', color: '#2db7f5' });
  if (
    currentScheme.winState == '0' &&
    !['COST_APPROVAL', 'BID_RESULT_CONFIRM', 'BIDDING', 'BID_PUSH'].includes(miceDetail.processNode)
  )
    res.push({ name: '中', detail: '中标方案', color: '#2db7f5' });
  return res;
};
const showSumItem = (subItem) => {
  let res = '';
  if (subItem.length > 1) {
    res = subItem.slice(0, -1).join('').replaceAll('=', '');
  } else {
    res = subItem.join('');
  }
  return res;
};
const demandRejectReason = ref<string>(''); // 驳回原因

const getList = async () => {
  loading.value = true;
  columnsSum.value = [
    { title: '项目', dataIndex: 'subject', key: 'subject', width: 20, fixed: 'left' },
    { title: '需求', dataIndex: 'demand', key: 'demand', width: 320 },
  ];
  schemeSum.value = [];
  columns.value = [];
  data.value = [];
  dataSum.value = [];
  hotelIdList.value = [];
  differentList.value = [];
  advantageList.value = [];
  let resDemand = await miceBidManOrderListApi.platformDetails({
    miceId: route.query?.miceId || JSON.parse(route.query.record).miceId,
  });
  if (resDemand.hotels.length == 1) isHotelDemandSubmittable.value = false;
  if (
    resDemand.processNode === resDemand.reverseProcessNode ||
    resDemand.processNode === resDemand.reverseAfterProcessNode
  ) {
    demandRejectReason.value = resDemand.demandRejectReason || resDemand.finalReverseReason; // 驳回原因
  }

  getConfig(resDemand);

  Object.assign(miceDetail, resDemand);

  // let resDemand = tempDemandRes;
  // 酒店需求
  let demandHotels = [];
  resDemand.hotels.forEach((item) => {
    hotelIdList.value.push(item);
    let tempArr = [
      { name: '位置', value: item.centerMarker },
      { name: '星级', value: dealData('level', item.level) },
    ];
    demandHotels.push(tempArr);
  });
  let paramTemp = {
    miceId: route.query?.miceId || JSON.parse(route.query.record).miceId,
    searchExcludeState: undefined,
    searchSelectedState: undefined,
    searchTargetState: undefined,
    schemeType: undefined,
  };
  if (route.path === '/bidman/scheme/index') {
  } else if (route.path === '/bidman/scheme/confirm') {
    if ($attrs.orderSource !== 'manage' || miceDetail.processNode !== 'SCHEME_RE_APPROVAL') {
      if (route.query.type === 'c') {
        paramTemp.schemeType = '1';
      } else {
        paramTemp.schemeType = '0';
        if ($attrs.orderSource !== 'manage') paramTemp.searchExcludeState = false;
      }
    }
  } else if (route.path === '/bidman/bid/index') {
  }

  let resScheme;
  if ($attrs.orderSource == 'user') resScheme = await schemeApi.schemeUserDetails(paramTemp);
  else resScheme = await schemeApi.schemePlatformDetails(paramTemp);
  // let resScheme = tempSchemeRes;
  let schemeList = [
    'hotels',
    'stays',
    'differences',
    'caterings',
    'places',
    'vehicles',
    'attendants',
    'activities',
    'insurances',
    'material',
    'presents',
    'others',
    'serviceFee',
  ];
  let arr;
  if (configCheck('SCHEME_APPROVAL', 'schemeApprovalProhibitMerchantConfigDefine'))
    arr = configCheck('SCHEME_APPROVAL', 'schemeApprovalProhibitMerchantConfigDefine')?.split(',') || [];
  resScheme = resScheme.map((item, index) => {
    differentList.value.push([]);
    advantageList.value.push([]);
    columnsSum.value.push({
      title: '方案' + (index + 1),
      dataIndex: 'filed' + (index + 1),
      key: 'filed' + (index + 1),
    });

    if (item.schemeDetails.length > 0) {
      item.schemeDetails.forEach((subItem) => {
        if (arr?.includes(String(subItem.merchantId))) item.ifban = true;
        item.isExcludeTemp = false;
        if (
          ['SCHEME_CONFIRM'].includes(miceDetail.processNode) &&
          configCheck('SCHEME_CONFIRM', 'schemeConfirmSelectedTypeConfigDefine') != '1'
        ) {
          item.unselectRemarks = '';
        }
        schemeList.forEach((type) => {
          if ((subItem[type] && subItem[type].length > 0) || subItem.material)
            if (item[type] === undefined) item[type] = subItem[type] || {};
            else {
              if (subItem[type] && item[type]) {
                if (type === 'material' && item[type].materialDetails.length > 0) {
                  Object.assign(item[type]?.materialDetails, subItem[type]?.materialDetails);
                } else Object.assign(item[type], subItem[type]);
              }
            }
        });
      });
    }
    item.schemeDetails = item.schemeDetails.sort((a, b) => {
      let res = 0;
      if (a.pdmMerchantPoolName.includes('旅行社') || a.pdmMerchantPoolName.includes('酒店')) res = -1;
      if (b.pdmMerchantPoolName.includes('旅行社') || a.pdmMerchantPoolName.includes('酒店')) res = 1;
      return res;
    });
    return item;
  });
  if ($attrs.orderSource === 'user' && miceDetail.processNode === 'SCHEME_RE_APPROVAL')
    resScheme = resScheme.filter((item) => {
      if (item.isSelected) return item;
    });
  schemeSum.value = resScheme;
  schemeSumInitiate.value = JSON.parse(JSON.stringify(resScheme));
  console.log('resScheme', resScheme);
  let dataObj = {
    hotels: [],
    stays: [],
    differences: [],
    caterings: [],
    places: [],
    vehicles: [],
    attendants: [],
    activities: [],
    insurances: [],
    material: [],
    presents: [],
    others: [],
    serviceFee: [],
  };
  // 初始化cols
  columns.value = [
    {
      name: 'leftTitle',
      dataIndex: 'leftTitle',
      sort: 0,
      key: 'leftTitle',
      fixed: 'left',
      colSpan: 3,
      customCell: (record, rowIndex, column) => {
        if (record.key === '1') {
          if (rowIndex === 0) return { colSpan: 3 };
        }
        if (record.key === '2') {
          return { colSpan: 0 };
        }
        if (column.dataIndex === 'leftTitle') {
          if (record.leftTitleR !== undefined) return { rowSpan: record.leftTitleR };
        }
      },
      width: 40,
    },
    {
      sort: 1,
      name: 'demand',
      dataIndex: 'demand',
      fixed: 'left',
      colSpan: 0,
      key: 'demand',
      customCell: (record, rowIndex, column) => {
        if (record.key === '1') {
          if (rowIndex === 0) return { colSpan: 0 };
        }
        if (record.key === '2') {
          return { colSpan: 3 + schemeSum.value.length };
        }
        if (record.leftTitleR !== undefined)
          return {
            rowSpan: record.leftTitleR,
          };
        if (record.leftTitleC !== undefined)
          return {
            colSpan: record.leftTitleC || 1,
          };
      },
      'min-width': 150,
    },
    {
      sort: 2,
      name: 'secondTitle',
      dataIndex: 'secondTitle',
      key: 'secondTitle',
      fixed: 'left',
      colSpan: 0,
      customCell: (record, rowIndex, column) => {
        if (record.key === '1') {
          if (rowIndex === 0) return { colSpan: 0 };
        }
        if (record.key === '2') {
          return { colSpan: 0 };
        }
        if (record.leftTitleC == 2)
          return {
            colSpan: 0,
          };
      },
      width: 120,
    },
  ];
  let DateSet = new Set();
  let DateList = {};
  let typeList = [
    {
      data: [],
      title: '住宿',
      name: 'stays',
      resFileds: [
        'miceSchemeHotelId',
        'roomType',
        'personNum',
        'schemeRoomNum',
        'discrepancyReason',
        'description',
        'schemeTotalPriceSt',
      ],
      titleList: ['酒店选择', '房型', '入住人数', '间夜数', '不一致原因', '方案备注', '方案金额'],
      symbolList: [
        {
          filed: 'miceSchemeHotelId',
          symbol: '：',
        },
        {
          filed: 'roomType',
        },
        // {
        //   filed: 'schemeUnitPrice',
        //   symbol: '*',
        // },
        {
          filed: 'schemeRoomNum',
          // unit: '(间夜数)',
          symbol: '=',
        },
        {
          money: true,
          filed: 'schemeTotalPriceSt',
        },
      ],
      demand: [
        { name: '酒店选择', filedValue: '：', value: '-', filed: 'miceDemandHotelId' },
        { name: '房型', filedValue: '', value: '-', filed: 'roomType' },
        { name: '入住人数', value: '-', filed: 'personNum' },
        { name: '间夜数', filedValue: '', value: '-', filed: 'roomNum' },
        { name: '不一致原因', value: '-', filed: 'discrepancyReason' },
        { name: '备注', value: '-', filed: 'description' },
      ],
    },
    {
      data: [],
      title: '住宿差异',
      name: 'differences',
      resFileds: [
        'schemeTotalPerson',
        'demandRoomType',
        'schemeOneRooms',
        'schemeTwoRooms',
        'schemeSuiteRooms',
        'reason',
      ],
      titleList: ['当日总人数', '方案合计', '大床房间夜数', '双床房间夜数', '套房间夜', '方案差异原因'],
      demand: [
        { name: '当日总人数', value: '-', filed: 'personNum' },
        { name: '方案合计', value: '-', filed: 'roomType' },
        { name: '大床房间夜数', value: '0', filed: 'room1' },
        { name: '双床房间夜数', value: '0', filed: 'room2' },
        { name: '套房间夜', value: '0', filed: 'room3' },
      ],
    },
    {
      data: [],
      title: '餐饮',
      name: 'caterings',
      resFileds: [
        'miceSchemeHotelId',
        'cateringType',
        'cateringTime',
        'schemePersonNum',
        'demandUnitPrice',
        'schemeUnitPrice',
        'isIncludeDrinks',
        'description',
        'schemeTotalPriceC',
      ],
      titleList: [
        '餐饮提供方',
        '用餐类型',
        '用餐时间',
        '人数',
        '用餐标准',
        '用餐单价',
        '是否含酒水',
        '备注',
        '方案金额',
      ],
      symbolList: [
        {
          filed: 'miceSchemeHotelId',
          symbol: '：',
        },
        {
          filed: 'cateringType',
        },
        // {
        //   filed: 'schemeUnitPrice',
        //   symbol: '*',
        // },
        {
          filed: 'schemePersonNum',
          symbol: '=',
        },
        {
          money: true,
          filed: 'schemeTotalPriceC',
        },
      ],
      demand: [
        { name: '餐饮提供方', filedValue: '：', value: '-', filed: 'isInsideHotel' },
        { name: '用餐类型', filedValue: '', value: '-', filed: 'cateringType' },
        { name: '用餐时间', value: '-', filed: 'cateringTime' },
        { name: '人数', filedValue: '', value: '-', filed: 'personNum' },
        { name: '用餐标准', value: '-', filed: 'demandUnitPrice' },
        { name: '是否含酒水', value: '-', filed: 'isIncludeDrinks' },
      ],
    },

    {
      data: [],
      title: '会场',
      name: 'places',
      resFileds: [
        'miceSchemeHotelId',
        'usageTime',
        'usagePurpose',
        'schemePersonNum',
        'area',
        'underLightFloor',
        'tableType',
        'schemePersonNum',
        'teaEachTotalPrice',
        'schemeUnitTeaPrice',
        'teaDesc',
        'schemeLedNum',
        'schemeUnitLedPrice',
        'schemeLedSource',
        'ledSpecs',
        'guildhall',
        'schemeUnitPlacePrice',
        'schemeTotalPriceP',
      ],
      titleList: [
        '酒店选择',
        '使用时间',
        '会场用途',
        '可容纳人数',
        '面积',
        '灯下层高',
        '摆台形式',
        '茶歇人数',
        '茶歇标准',
        '茶歇单价',
        '茶歇说明',
        'LED数量（个）',
        'LED单价',
        'LED来源',
        'LED规格描述',
        '会议厅选择',
        '会场单价',
        '方案金额',
      ],
      symbolList: [
        {
          filed: 'guildhall',
          symbol: '：',
        },
        {
          title: '茶歇',
          filed: 'schemePersonNum',
          // symbol: showMoney.value ? '茶歇' : '茶歇',
        },
        {
          title: 'LED',
          filed: 'schemeLedNum',
          symbol: '个=',
        },
        {
          money: true,
          filed: 'schemeTotalPriceP',
        },
      ],
      demand: [
        { name: '餐饮提供方', filedValue: '：', value: '-', filed: 'miceDemandHotelId' },
        { name: '使用时间', value: '-', filed: 'usageTime' },
        { name: '会场用途', filedValue: '', value: '-', filed: 'usagePurpose' },
        { name: '参会人数', value: '-', filed: 'personNum' },
        { name: '面积', value: '-', filed: 'area' },
        { name: '灯下层高', value: '-', filed: 'underLightFloor' },
        { name: '摆台形式', value: '-', filed: 'tableType' },
        { name: '茶歇标准', value: '-', filed: 'teaEachTotalPrice' },
        { name: '茶歇说明', value: '-', filed: 'teaDesc' },
        { name: 'LED数量（个）', value: '-', filed: 'ledNum' },
        { name: 'LED规格描述', value: '-', filed: 'ledSpecs' },
      ],
    },
    {
      data: [],
      title: '用车',
      name: 'vehicles',
      resFileds: [
        'usageType',
        'seats',
        'brand',
        'usageTime',
        'schemeVehicleNum',
        'schemeUnitPrice',
        'route',
        'schemeTotalPriceV',
      ],
      titleList: ['用车方式', '车型', '型号', '用车时间', '数量', '单价', '路线', '方案金额'],
      symbolList: [
        {
          filed: 'usageType',
          symbol: '：',
        },
        {
          filed: 'brand',
        },
        // {
        //   filed: 'schemeUnitPrice',
        //   symbol: '*',
        // },
        {
          filed: 'schemeVehicleNum',
          unit: '辆',
          symbol: '=',
        },
        {
          money: true,
          filed: 'schemeTotalPriceV',
        },
      ],
      demand: [
        { name: '用车方式', value: '-', filed: 'usageType' },
        { name: '型号', filedValue: '：', value: '-', filed: 'brand' },
        { name: '车型', filedValue: '', value: '-', filed: 'seats' },
        { name: '用车时间', value: '-', filed: 'usageTime' },
        { name: '数量', filedValue: '', value: '-', filed: 'vehicleNum' },
        { name: '路线', value: '-', filed: 'route' },
      ],
    },
    {
      data: [],
      title: '服务人员',
      name: 'attendants',
      resFileds: ['type', 'schemePersonNum', 'duty', 'schemeUnitPrice', 'schemeTotalPriceA'],
      titleList: ['人员职责', '数量', '工作范围', '单价', '方案金额'],
      symbolList: [
        {
          filed: 'type',
          symbol: '：',
        },

        // {
        //   filed: 'schemeUnitPrice',
        //   symbol: '*',
        // },
        {
          filed: 'schemePersonNum',
          symbol: '=',
        },
        {
          money: true,
          filed: 'schemeTotalPriceA',
        },
      ],
      demand: [
        { name: '人员职责', filedValue: '：', value: '-', filed: 'type' },
        { name: '数量', filedValue: '', value: '-', filed: 'personNum' },
        { name: '工作范围', value: '-', filed: 'duty' },
      ],
    },
    {
      data: [],
      title: '拓展活动',
      name: 'activities',
      resFileds: [
        'schemePersonNum',
        'demandUnitPrice',
        'schemeUnitPrice',
        'description',
        // 'paths',
        'schemeTotalPriceAc',
      ],
      titleList: [
        '参与人数',
        '费用标准',
        '拓展报价',
        '活动说明',
        // '附件',
        '方案金额',
      ],
      symbolList: [
        {
          filed: 'titleFiled',
          symbol: '：',
        },

        // {
        //   filed: 'schemeUnitPrice',
        //   symbol: '*',
        // },
        {
          filed: 'schemePersonNum',
          symbol: '=',
        },
        {
          money: true,
          filed: 'schemeTotalPriceAc',
        },
      ],
      demand: [
        { name: '活动说明', filedValue: 'titleFiled', value: '-', filed: 'description' },
        { name: '参与人数', filedValue: '', value: '-', filed: 'personNum' },
        { name: '费用标准', value: '-', filed: 'demandUnitPrice' },
        { name: '附件', value: '-', filed: 'paths' },
      ],
    },
    {
      data: [],
      title: '保险',
      name: 'insurances',
      resFileds: ['schemePersonNum', 'insuranceName', 'demandUnitPrice', 'schemeUnitPrice', 'schemeTotalPriceI'],
      titleList: ['参保人数', '保险产品', '费用标准', '单价', '方案金额'],
      symbolList: [
        {
          filed: 'insuranceName',
          symbol: '：',
        },

        // {
        //   filed: 'schemeUnitPrice',
        //   symbol: '*',
        // },
        {
          filed: 'schemePersonNum',
          symbol: '=',
        },
        {
          money: true,
          filed: 'schemeTotalPriceI',
        },
      ],
      demand: [
        { name: '保险产品', filedValue: '：', value: '-', filed: 'insuranceName' },
        { name: '参保人数', filedValue: '', value: '-', filed: 'personNum' },
        { name: '费用标准', value: '-', filed: 'demandUnitPrice' },
      ],
    },
  ];
  let typeListOthers = [
    {
      data: [],
      title: '布展物料',
      name: 'material',
      resFileds: ['type', 'demandUnitPrice', 'schemeUnitPrice', 'schemeMaterialNum', 'schemeUnitPriceM'],
      titleList: ['物料', '费用标准', '方案报价', '数量', '方案金额'],
      symbolList: [
        {
          filed: 'type',
          symbol: '：',
        },
        // {
        //   filed: 'schemeUnitPrice',
        //   symbol: '*',
        // },
        {
          filed: 'schemeMaterialNum',
          // unit: '个',
          symbol: '=',
        },
        {
          filed: 'schemeUnitPriceM',
          money: true,
          unit: '',
        },
      ],
      demand: [
        { name: '物料', filedValue: '：', value: '-', filed: 'type' },
        { name: '费用标准', value: '-', filed: 'unitPrice' },
        { name: '数量', filedValue: '', value: '-', filed: 'num' },
      ],
    },
    {
      data: [],
      title: '礼品',
      name: 'presents',
      resFileds: [
        'productName',
        'schemePersonNum',
        'schemeTotalPrice',
        'schemeTotalPrice',
        'deliveryDate',
        'schemeTotalPrice',
      ],
      titleList: ['礼品描述', '数量', '总预算', '方案报价', '送达日期', '方案金额'],
      symbolList: [
        {
          filed: 'productName',
          symbol: '：',
        },
        {
          filed: 'schemePersonNum',
          symbol: '=',
        },
        {
          money: true,
          filed: 'schemeTotalPrice',
        },
      ],
      demand: [
        { name: '礼品描述', filedValue: '：', value: '-', filed: 'productName' },
        { name: '数量', filedValue: '', value: '-', filed: 'personNum' },
        { name: '总预算', value: '-', filed: 'demandTotalPrice' },
        { name: '送达日期', value: '-', filed: 'deliveryDate' },
      ],
    },
    {
      data: [],
      title: '其它',
      name: 'others',
      resFileds: ['itemName', 'num', 'demandTotalPrice', 'specs', 'schemeTotalPriceO'],
      titleList: ['项目', '数量', '总预算', '备注', '方案金额'],
      symbolList: [
        {
          filed: 'itemName',
          symbol: '：',
        },
        {
          filed: 'num',
          symbol: '=',
        },
        {
          money: true,
          filed: 'demandTotalPrice',
        },
      ],
      demand: [
        { name: '项目', filedValue: '：', value: '-', filed: 'itemName' },
        { name: '数量', filedValue: '', value: '-', filed: 'num' },
        { name: '总预算', value: '-', filed: 'demandTotalPrice' },
        { name: '备注', value: '-', filed: 'specs' },
      ],
    },
    {
      data: [],
      title: '全单服务费',
      name: 'serviceFee',
      resFileds: ['serviceFeeRate', 'schemeTotalPriceS'],
      titleList: ['收取比例', '服务费'],
      demand: '/',
      symbolList: [
        {
          money: true,
          filed: 'schemeTotalPriceS',
        },
      ],
      // demand: [{ name: '收取上限比例', filedValue: '；', value: '-', filed: 'serviceFeeRate' }],
    },
  ];
  typeList.forEach((item, index) => {
    let row = { subject: item.title, demand: [] };

    if (resDemand[item.name] || item.name === 'differences') {
      let tempType = item.name;
      if (tempType === 'differences') tempType = 'stays';
      resDemand[tempType]?.forEach((subItem, subIndex) => {
        row.demand.push([]);

        if (item[subItem.demandDate + 'demandData']) {
          item[subItem.demandDate + 'demandData'].push(subItem);
        } else {
          item[subItem.demandDate + 'demandData'] = [subItem];
        }
        if (item.name === 'differences') {
          if (item[subItem.demandDate + 'demand'] === undefined)
            item[subItem.demandDate + 'demand'] = [JSON.parse(JSON.stringify(item.demand))];
        } else {
          if (item[subItem.demandDate + 'demand'] === undefined) item[subItem.demandDate + 'demand'] = [];
          item[subItem.demandDate + 'demand'].push([...JSON.parse(JSON.stringify(item.demand))]);
        }

        if (item.name === 'differences') {
          item[subItem.demandDate + 'demand'][0][subItem.roomType + 1].value = subItem.schemeRoomNum || '-';
        }
        item[subItem.demandDate + 'demand'].forEach((list, listIndex) => {
          list.forEach((demandItem, demandIndex) => {
            let tempDemand = item[subItem.demandDate + 'demandData'][listIndex][demandItem.filed];
            if (demandItem.filed) {
              if (item.name === 'differences') {
                let differObj = {
                  personNum: 0,
                  roomType: '',
                  room1: 0,
                  room2: 0,
                  room3: 0,
                };
                item[subItem.demandDate + 'demandData'].forEach((stay) => {
                  differObj.personNum += stay.personNum;
                  let curRoomType = dealData('roomType', stay.roomType, resDemand.hotels, stay, item.name);
                  if (!differObj.roomType.includes(curRoomType)) differObj.roomType += curRoomType + '+';
                  differObj['room' + stay.roomType] += stay.roomNum;
                });
                differObj.roomType = differObj.roomType.slice(0, -1);
                demandItem.value =
                  differObj[demandItem.filed] +
                  (demandItem.filed.length < 6 && demandItem.filed.includes('room') ? '间夜' : '');
              } else {
                if (demandItem.name === '路线' && list[0].value === '包车') demandItem.name = '路线概述';
                demandItem.value = dealData(
                  demandItem.filed,
                  tempDemand,
                  resDemand.hotels,
                  item[subItem.demandDate + 'demandData'][listIndex],
                );
              }
            }
          });
        });

        item['demand'].forEach((demandItem, demandIndex) => {
          // if (subIndex === resDemand[tempType].length - 1)
          // 表格合计模块，subIndex最后数据添加完成
          // if (subIndex === resDemand[tempType].length - 1)
          if (demandItem.filedValue !== undefined) {
            let value =
              dealData(
                demandItem.filed,
                subItem[demandItem.filed],
                resDemand.hotels,
                undefined,
                undefined,
                item.title,
              ) +
              // (row.demand[subIndex].length === 2 ? `(${demandItem.name})` : '') +
              demandItem.filedValue;
            if (demandItem.filedValue === 'titleFiled') value = item.title + '：';
            row.demand[row.demand.length - 1].push(value);
          }
        });
      });
    } else item.demand = [];
    if (!['住宿差异'].includes(item.title)) {
      dataSum.value.push(row);
    }
  });
  typeListOthers.forEach((item) => {
    let row = { subject: item.title, demand: [] };
    if (resDemand[item.name]) {
      let tempArr = resDemand[item.name];
      if (item.name === 'material') tempArr = resDemand[item.name].materialDetails;
      if (item.name === 'serviceFee') tempArr = [tempArr];
      tempArr?.forEach((subItem, subIndex) => {
        row.demand.push([]);
        if (item.demandList) {
          item.demandList.push(JSON.parse(JSON.stringify(item.demand)));
        } else item.demandList = [JSON.parse(JSON.stringify(item.demand))];
        item.demandList[subIndex].forEach((demandItem, demandIndex) => {
          demandItem.value = dealData(
            demandItem.filed,
            subItem[demandItem.filed],
            resDemand.hotels,
            subItem,
            undefined,
            item.title,
          );
          if (demandItem.filedValue !== undefined)
            row.demand[subIndex].push(
              demandItem.value +
                (row.demand[subIndex].length === 2 ? `(${demandItem.name})` : '') +
                demandItem.filedValue,
            );
        });
      });
    } else item.demand = [];
    dataSum.value.push(row);
  });
  //方案循环
  schemeSum.value.forEach((item, index) => {
    // if(type==='hotels') advantageList.value[advantageList.value.length-1].push()
    // 判断columns列数
    columns.value.push({
      sort: index + 3,
      title: 'filed' + (index + 1),
      dataIndex: 'filed' + (index + 1),
      key: 'scheme',
      customCell: (record, rowIndex, column) => {
        if (record.key === '2') {
          return { colSpan: 0 };
        }
      },
      // width: '350px',
      showMore: false,
    });
    // 酒店循环
    for (let i = 0; i < item.hotels?.length; i++) {
      for (let key in item.hotels[i]) {
        dataObj.hotels[i] = { ...dataObj.hotels[i], [key + (index + 1)]: item.hotels[i][key] };
      }
    }
    typeList.forEach((type, typeIndex) => {
      // 合计计算
      let tempSchemeV = [];
      let money = 0;
      for (let i = 0; i < item[type.name]?.length; i++) {
        tempSchemeV.push([]);
        if (type.symbolList)
          type.symbolList.forEach((symbol) => {
            let value;
            if (symbol.filed === 'titleFiled') value = type.title;
            else value = dealData(symbol.filed, item[type.name][i][symbol.filed], item.hotels, item[type.name][i]);
            if (value == '-') value = '';
            if (symbol.money) money += Number(value?.split('元')[0]);
            let contactStr = value;
            if (['schemePersonNum', 'schemeUnitTeaPrice'].includes(symbol.filed) && ['会场'].includes(type.title)) {
              if (!item[type.name][i].hasTea) contactStr = '';
              else contactStr += symbol.symbol ? symbol.symbol : '';
            } else if (['schemeLedNum', 'schemeUnitLedPrice'].includes(symbol.filed) && ['会场'].includes(type.title)) {
              if (!item[type.name][i].hasLed) {
                contactStr = ['schemeUnitLedPrice'].includes(symbol.filed) ? '=' : '';
              } else {
                contactStr += symbol.symbol ? symbol.symbol : '';
              }
            } else if (['schemeUnitPlacePrice'].includes(symbol.filed) && ['会场'].includes(type.title)) {
              if (item[type.name][i].hasTea) contactStr += (symbol.unit ? symbol.unit : '') + '+茶歇';
              else if (item[type.name][i].hasLed) contactStr += (symbol.unit ? symbol.unit : '') + '+LED';
            } else contactStr += (symbol.unit ? symbol.unit : '') + (symbol.symbol ? symbol.symbol : '');

            tempSchemeV[i].push((contactStr ? (symbol.title ? symbol.title : '') : '') + contactStr);
          });
      }
      if (type.title !== '住宿差异') {
        if (showMoney.value) tempSchemeV.push(['合计：', `${money}元`]);
        let num = typeIndex;
        if (typeIndex > 1) num = num - 1;
        dataSum.value[num]['filed' + (index + 1)] = tempSchemeV;
      }

      item[type.name]?.forEach((subItem, subIndex) => {
        let tempDate = subItem.demandDate || subItem.differenceDate;
        if (tempDate) {
          DateSet.add(tempDate);
          if (!DateList[tempDate]) {
            DateList[tempDate] = {};
          }

          let sumDate = DateList[tempDate];
          if (sumDate[type.name] === undefined) {
            sumDate[type.name] = [];
            for (let i = 0; i < schemeSum.value.length; i++) {
              sumDate[type.name].push([]);
            }
          }
          sumDate[type.name][index].push(subItem);
        }
      });
    });
    typeListOthers.forEach((type, typeIndex) => {
      let curArr = item[type.name];
      if (type.name === 'material') curArr = curArr?.materialDetails;
      if (type.name === 'serviceFee') curArr = [curArr];
      if (type.name === 'presents') {
        curArr = curArr?.map((item) => {
          return { ...item, ...item.presentDetails[0] };
        });
      }
      // 合计计算
      let tempSchemeV = [];
      let money = 0;
      for (let i = 0; i < curArr?.length; i++) {
        tempSchemeV.push([]);
        if (type.symbolList)
          type.symbolList.forEach((symbol) => {
            if (curArr[i]) {
              let value = dealData(symbol.filed, curArr[i][symbol.filed], item.hotels, curArr[i], '', type.title);
              if (symbol.money) money += Number(value?.split('元')[0]);
              tempSchemeV[i].push(value + (symbol.unit ? symbol.unit : '') + (symbol.symbol ? symbol.symbol : ''));
            }
          });
      }
      if (showMoney.value) tempSchemeV.push(['合计：', `${money}元`]);
      let num = typeIndex;
      dataSum.value[typeList.length - 1 + num]['filed' + (index + 1)] = tempSchemeV;

      let list = item[type.name] || [];
      if (type.name === 'material') {
        list = item[type.name]?.materialDetails;
      }
      if (type.name === 'presents') {
        list = [item[type.name]][0].map((item) => {
          return {
            ...item,
            ...item.presentDetails[0],
          };
        });
      }
      if (type.name === 'serviceFee') {
        list = [item[type.name]];
      }
      // 住宿循环
      for (let i = 0; i < list?.length; i++) {
        for (let key in list[i]) {
          dataObj[type.name][i] = { ...dataObj[type.name][i], [key + (index + 1)]: list[i][key] };
        }
      }
    });
  });
  let sumMoney = {
    subject: '合计',
    demand: '/',
  };
  if (showMoney.value) {
    dataSum.value.forEach((item, index) => {
      for (let i = 0; i < schemeSum.value.length; i++) {
        let arrScheme = item['filed' + (i + 1)] || [];
        if (arrScheme.length > 0) {
          if (sumMoney['filed' + (i + 1)] === undefined)
            sumMoney['filed' + (i + 1)] = schemeSum.value[i]?.schemeCombinationTotalPrice || '-';
        }
      }
    });
    for (let key in sumMoney) {
      if (key.includes('filed') && sumMoney[key] !== '-') sumMoney[key] += '元';
    }
    dataSum.value.push(sumMoney);
  } else {
  }
  let advantageValue = '';
  let hotalNameAge = '';
  let advantageValueDistance = Infinity;
  let hotalNameDistance = '';
  //     let advantageNum = 0;
  // let advantageNumDistance = 0;
  dataObj.hotels.forEach((item, index) => {
    for (let i = 0; i < schemeSum.value.length; i++) {
      if (advantageValue < item['decorationYear' + (i + 1)]) {
        // advantageNum = i;
        advantageValue = item['decorationYear' + (i + 1)];
        hotalNameAge = item['hotelName' + (i + 1)];
      }
      if (item['distance' + (i + 1)] && advantageValueDistance > item['distance' + (i + 1)]) {
        // advantageNumDistance = i;
        advantageValueDistance = item['distance' + (i + 1)];
        hotalNameDistance = item['hotelName' + (i + 1)];
      }
    }
  });
  for (let i = 0; i < schemeSum.value.length; i++) {
    schemeSum.value[i].hotels.forEach((item) => {
      let ageValue = hotalNameAge + '：装修年份为' + advantageValue + '年';
      let distanceValue = hotalNameDistance + '：酒店距离为' + advantageValueDistance + 'KM';
      if (advantageValue == item['decorationYear'] && !advantageList.value[i].includes(ageValue)) {
        advantageList.value[i].push(ageValue);
      }
      if (
        ![null, Infinity].includes(advantageValueDistance) &&
        advantageValueDistance == item['distance'] &&
        !advantageList.value[i].includes(distanceValue) &&
        !isHotelDemandSubmittable.value
      ) {
        advantageList.value[i].push(distanceValue);
      }
    });
  }
  let DateArr = Array.from(DateSet).sort((a, b) => getNumber(a) - getNumber(b));

  data.value = [];
  let resFileds = ['hotelName', 'cityName', 'level'];
  let titleList = ['酒店名称', '地址', '星级'];
  data.value = data.value.concat(dealTableData(dataObj.hotels, demandHotels, resFileds, titleList, '酒店'));
  // 循环日期
  for (let time of DateArr) {
    if (time)
      data.value.push({
        key: '2',
        leftTitle: '',
        secondTitle: '',
        demand: (time || '') + '需求',
        type: 'firstTitle',
        filed1: '',
        filed2: '',
        filde3: '',
      });
    // 循环每日计划
    typeList.forEach((type) => {
      let curList = DateList[time][type.name];
      if (curList) {
        let tempData = [];

        let maxLength = 0;
        curList.forEach((item) => {
          maxLength = Math.max(maxLength, item.length);
        });
        for (let i = 0; i < maxLength; i++) {
          let objSum = {};
          curList.forEach((item, index) => {
            for (let key in item[i]) {
              objSum[key + (index + 1)] = item[i][key];
            }
          });

          tempData.push(objSum);
        }
        data.value = data.value.concat(
          dealTableData(tempData, type[time + 'demand'], type.resFileds, type.titleList, type.title, time),
        );
      }
    });
  }
  typeListOthers.forEach((type) => {
    if (dataObj[type.name].length > 0) {
      data.value = data.value.concat(
        dealTableData(dataObj[type.name], type.demandList, type.resFileds, type.titleList, type.title),
      );
    }
  });
  if (!showMoney.value) {
    data.value = data.value.filter((item) => {
      if (
        ![
          '方案金额',
          '用餐单价',
          '方案金额',
          '茶歇单价',
          'LED单价',
          '会场单价',
          '单价',
          '拓展报价',
          '方案报价',
          // '费用标准',
          '服务费',
        ].includes(item.secondTitle)
      )
        return item;
    });
    dataSum.value = dataSum.value.filter((item) => {
      if (item.demand.length > 0) return item;
    });
  }
  // 处理合计数据
  dealSumData();
  loading.value = false;
};
const excludeScheme = (column, type) => {
  currentSchemeNum.value = column.sort - 3;
  if (type === 'isSelected')
    schemeSum.value[currentSchemeNum.value].isSelected = !schemeSum.value[currentSchemeNum.value].isSelected;
  else if (type === 'isExclude') {
    if (schemeSum.value[currentSchemeNum.value].isExclude) {
      handleOk();
    } else {
      schemeSum.value[currentSchemeNum.value].excludeRemarks = '';

      open.value = true;
    }
  }
  if (type === 'isExcludeTemp') {
    if (schemeSum.value[currentSchemeNum.value].isExcludeTemp) handleOkTemp();
    else openExcludeTemp.value = true;
  }
};
// 方案审核阶段排除
const handleOk = () => {
  if (schemeSum.value[currentSchemeNum.value].excludeRemarks) {
    schemeSum.value[currentSchemeNum.value].isExclude = !schemeSum.value[currentSchemeNum.value].isExclude;
    open.value = false;
  } else {
    message.error('排除原因不能为空！');
  }
};
// 方案确认阶段排除
const handleOkTemp = () => {
  if (schemeSum.value[currentSchemeNum.value].unselectRemarks) {
    schemeSum.value[currentSchemeNum.value].isExcludeTemp = !schemeSum.value[currentSchemeNum.value].isExcludeTemp;
    openExcludeTemp.value = false;
  } else {
    message.error('排除原因不能为空！');
  }
};
const schemeRes = () => {
  router.push({
    path: '/bidman/scheme/publish/index',
    query: {
      record: JSON.stringify({
        miceId: route.query?.miceId || JSON.parse(route.query.record).miceId,
        miceDemandId: miceDetail.id,
        status: 'scheme',
        hotels: hotelIdList.value,
        hideBtn: hideBtn.value,
      }),
    },
  });
  // 关闭当前页签
  isCloseLastTab.value = true;
};

const showBidPush = ref(false);
const showBidPushed = ref(false);
const handlePush = async () => {
  try {
    // 校验：如果修改了截止时间但未上传证明材料
    if (isPublishTimeChanged.value && attachmentList.value.length == 0) {
      message.error('请上传证明材料');
      return;
    }
    if (isPublishSchemeChanged.value && attachmentListPush.value.length == 0) {
      message.error('请上传证明材料');
      return;
    }
    loading.value = true;

    let tempPublishHour = publishHour.value + ':00:00';

    let list = [];
    bidData.value.forEach((item) => {
      list.push({
        merchantType: item.merchantType,
        pushStrategy: item.scheme === '/' ? 1 : item.scheme,
      });
    });
    let res = await schemeApi.bidPush({
      miceId: route.query?.miceId || JSON.parse(route.query.record).miceId,
      deadline: publishDate.value
        ? dayjs(publishDate.value).format('YYYY-MM-DD') + ' ' + tempPublishHour
        : formattedDate,
      merchantTypeStrategy: list,
      deadlineAttachment:
        attachmentList.value.map((item) => {
          return JSON.stringify({
            name: item.name,
            url: item.filePath,
          });
        }) || undefined,
      pushStrategyAttachment:
        attachmentListPush.value.map((item) => {
          return JSON.stringify({
            name: item.name,
            url: item.filePath,
          });
        }) || undefined,
    });
    if (res?.success) {
      message.success('提交成功！');
      showBidPush.value = false;
      let pushedData = await schemeApi.pushedDetails({
        miceId: route.query?.miceId || JSON.parse(route.query.record).miceId,
      });
      bidedData.value = [];
      let num = 0;
      pushedData.merchantTypes.forEach((item, index) => {
        item.schemes.forEach((item1, index1) => {
          num++;
          let arr = [];
          item1.merchants.forEach((item2) => {
            let tempItem = item2;
            tempItem.bidMerchantName = item2.bidMerchantName;
            tempItem.schemeName = `方案${num}`;
            arr.push(item2);
          });
          bidedData.value.push({
            merchantType: item.merchantType,
            rowSpan: item.schemes.length == 1 ? 1 : index1 === 0 ? item.schemes.length : 0,
            type: MerchantType.ofType(item.merchantType)?.desc,
            num: item.bidNum,
            schemeName: `方案${num}`,
            biddingDeadline: pushedData.biddingDeadline,
            scheme: arr,
          });
        });
      });
      showBidPushed.value = true;
    }
  } finally {
    loading.value = false;
  }
};
const configNode = reactive({});
const isHotelDemandSubmittable = ref(true);

const getConfig = async (demand) => {
  let arr = ['SCHEME_APPROVAL', 'SCHEME_CONFIRM', 'BID_PUSH'];
  const res = await miceBidManOrderListApi.processDetails({
    id: demand.pdMainId,
    verId: demand.pdVerId,
  });
  // isHotelDemandSubmittable.value = res.isHotelDemandSubmittable;
  res.nodes.forEach((item) => {
    arr.forEach((type) => {
      if (type.includes(item.metaKey)) {
        configNode[type] = item;
      }
    });
  });
};

const configCheck = (type, str) => {
  let res = false;
  configNode[type]?.configs.forEach((item) => {
    if (item.metaKey === str) {
      res = item.configParam;
    }
  });
  return res;
};
const record = reactive({});
onMounted(() => {
  if (resolveParam(route.query.record));
  Object.assign(record, resolveParam(route.query.record));
  hideBtn.value = record?.hideBtn || '';
  if (['/bidman/scheme/index', '/bidman/scheme/confirm'].includes(route.path) || $attrs.orderSource === 'user') {
    if (['MICE_PENDING', 'MICE_EXECUTION'].includes(record.processNode)) showMoney.value = true;
    else showMoney.value = false;
  }
  getList();
});
const hours = ref<string[]>([
  '00',
  '01',
  '02',
  '03',
  '04',
  '05',
  '06',
  '07',
  '08',
  '09',
  '10',
  '11',
  '12',
  '13',
  '14',
  '15',
  '16',
  '17',
  '18',
  '19',
  '20',
  '21',
  '22',
  '23',
]);
const publishDate = ref<Dayjs>();
const publishHour = ref<Dayjs>();
const currentHour = ref<string>('');
const currentPublishDate = ref<Dayjs>();
const currentPublishHour = ref<Dayjs>();

const getCurrentHours = () => {
  if (dayjs(publishDate.value).format('YYYY-MM-DD') == dayjs(currentPublishDate.value).format('YYYY-MM-DD')) {
    publishHour.value = currentPublishHour.value;
  }
};
// 禁止选择前一天时间
const disabledDate = (current) => {
  const today = dayjs();
  return current.isBefore(today, 'day');
};
const handleChange = (e) => {
  console.log(e);
};
defineExpose({
  saveScheme,
  miceDetail,
  getList,
  schemeSum,
  schemeSumInitiate,
  saveSchemeConfirm,
});
</script>

<template>
  <div class="scheme-container" :style="$attrs.orderSource === 'user' ? '' : ''">
    <a-spin :spinning="loading">
      <a-alert
        v-if="demandRejectReason"
        class="mb16 demand_reject_reason"
        message="驳回原因："
        :description="demandRejectReason"
        show-icon
        type="warning"
      />
      <miceHeader id="widthTable" :miceDetail="miceDetail"> </miceHeader>
      <div class="scheme-tip" v-show="!['SCHEME_CONFIRM'].includes(miceDetail.processNode)">
        <div>
          <img src="@/assets/image/scheme/tip.png" width="22" alt="" />
          以下{{ schemeSum.length }}套方案为最终组合执行方案，确认后将由用户进行方案挑选。
        </div>
        <span class="result" @click="schemeRes"> 方案互动结果 </span>
      </div>
      <div
        class="hotel"
        v-if="schemeSum.length > 0"
        :style="['SCHEME_CONFIRM'].includes(miceDetail.processNode) ? 'margin-top:10px' : ''"
      >
        <a-table
          :columns="columns"
          v-if="columns.length > 0"
          :data-source="data"
          :pagination="false"
          bordered
          :scroll="{
            x: schemeSum.length < 5 ? undefined : `${400 + 180 * schemeSum.length}px`,
            y: `calc(100vh - ${
              310 -
              ($attrs.orderSource !== 'manage' ? 20 : 0) -
              (record.hideBtn ? (record?.hideBtn == '1' ? 100 : 0) : 0)
            }px)`,
          }"
        >
          <template #headerCell="{ title, column }">
            <div class="scheme-different" v-if="['leftTitle', 'demand', 'secondTitle'].includes(column.key)">
              <a-checkbox @change="handleChange" v-model:checked="checked">高亮不同项</a-checkbox>
            </div>
            <!-- 用户-方案确认 -->
            <!-- 方案审核 -->
            <div
              class="scheme-item"
              id="my-element"
              v-else-if="column.key === 'scheme' && route.path === '/bidman/scheme/index'"
            >
              <div class="exclude-icon" v-show="schemeSum[column.sort - 3].isExclude"></div>
              <div class="item-title">
                <div class="title" :style="schemeSum[column.sort - 3].isExclude ? 'color:#C9CDD4' : ''">
                  方案{{ column.sort - 2 }}
                </div>
                <div style="display: inline-block; margin-left: 5px" v-if="getSchemeName(column).length > 0">
                  <a-tooltip v-for="tag in getSchemeName(column)" :key="tag.name">
                    <template #title>{{ tag.detail }}</template>
                    <a-tag :color="tag.color">{{ tag.name }}</a-tag>
                  </a-tooltip>
                </div>

                <!-- <div class="money">¥15,765.00</div> -->
                <a-tooltip class="btn-tooltip" v-if="schemeSum[column.sort - 3].ifban" placement="top">
                  <template #title>
                    <span>方案服务商禁止排除</span>
                  </template>
                  <a-button class="fixed1-btn" size="small" :disabled="true" shape="round">{{ '禁止排除' }}</a-button>
                </a-tooltip>
                <a-button
                  v-else
                  class="fixed1-btn"
                  size="small"
                  :type="!schemeSum[column.sort - 3].isExclude ? 'primary' : 'Default'"
                  shape="round"
                  @click="excludeScheme(column, 'isExclude')"
                  >{{ schemeSum[column.sort - 3].isExclude ? '取消' : '排除' }}</a-button
                >
              </div>
              <div class="merchant">
                <div
                  class="row"
                  v-for="(item, index) in schemeSum[column.sort - 3].schemeDetails"
                  :key="item.id"
                  v-show="index == 0"
                >
                  <div class="merchant-name">{{ item.pdmMerchantPoolName }}</div>
                  <div class="top-title">{{ item?.title }}</div>

                  <div
                    class="more"
                    v-if="schemeSum[column.sort - 3].schemeDetails.length > 1"
                    v-show="index === 0 ? true : false"
                  >
                    <a-popover trigger="click">
                      <template #content>
                        <div class="merchant" style="margin-top: 0px">
                          <div
                            class="row1"
                            v-for="(item, index) in schemeSum[column.sort - 3].schemeDetails"
                            :key="item.id"
                          >
                            <div class="merchant-name">{{ item.pdmMerchantPoolName }}</div>
                            <div class="top-title">{{ item?.title }}</div>
                            <div class="pool">
                              <div
                                class="poot-btn"
                                v-for="subItem in computedMethod(item.pdmMerchantPoolItems)"
                                :key="subItem"
                              >
                                {{ MiceItemConstant.ofType(subItem).desc }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                      {{ column.showMore ? '收起' : '更多' }}
                    </a-popover>
                  </div>
                </div>
              </div>
              <div class="scheme-details flex">
                <a-tooltip
                  v-if="differentList[column.sort - 3].length > 0"
                  color="#f50"
                  :overlayInnerStyle="{ width: '300px' }"
                >
                  <template #title>
                    <div>
                      <p v-for="text in differentList[column.sort - 3]" :key="text">{{ text }}</p>
                    </div>
                  </template>
                  <div class="details-different">
                    <div><img src="@/assets/image/scheme/LikeOutline.png" width="16" alt="" /></div>
                    <div class="differ-name">
                      差异<span class="count">({{ differentList[column.sort - 3].length }}) </span>
                    </div>
                    <div><img src="@/assets/image/scheme/triangle.png" width="12" alt="" /></div>
                  </div>
                </a-tooltip>
                <div v-else class="details-different">
                  <div><img src="@/assets/image/scheme/LikeOutline.png" width="16" alt="" /></div>
                  <div class="differ-name">
                    差异<span class="count">({{ differentList[column.sort - 3].length }}) </span>
                  </div>
                  <div><img src="@/assets/image/scheme/triangle.png" width="12" alt="" /></div>
                </div>
                <a-divider type="vertical" style="height: 18px" />
                <a-tooltip color="#52c41a" v-if="advantageList[column.sort - 3].length > 0">
                  <template #title>
                    <div>
                      <p v-for="text in advantageList[column.sort - 3]" :key="text">{{ text }}</p>
                    </div>
                  </template>
                  <div class="details-advantage">
                    <div><img src="@/assets/image/scheme/advantage.png" width="16" alt="" /></div>
                    <div class="differ-name">
                      优势<span class="count">({{ advantageList[column.sort - 3].length }}) </span>
                    </div>
                    <div><img src="@/assets/image/scheme/triangle.png" width="12" alt="" /></div>
                  </div>
                </a-tooltip>
                <div v-else class="details-advantage">
                  <div><img src="@/assets/image/scheme/advantage.png" width="16" alt="" /></div>
                  <div class="differ-name">
                    优势<span class="count">({{ advantageList[column.sort - 3].length }}) </span>
                  </div>
                  <div><img src="@/assets/image/scheme/triangle.png" width="12" alt="" /></div>
                </div>
              </div>
            </div>

            <div
              class="scheme-item"
              id="my-element"
              v-else-if="
                column.key === 'scheme' &&
                route.path === '/bidman/scheme/confirm' &&
                !(
                  (miceDetail.processNode === 'SCHEME_CONFIRM' && $attrs.orderSource === 'manage') ||
                  miceDetail.processNode === 'SCHEME_RE_APPROVAL' ||
                  (['SCHEME_APPROVAL', 'BID_PUSH', 'BIDDING', 'BID_RESULT_CONFIRM']
                    .concat(arrConfirmView)
                    .includes(miceDetail.processNode) &&
                    resolveParam(route.query.record).orderType === 'detail')
                )
              "
            >
              <div class="item-title">
                <div class="exclude-icon" v-show="schemeSum[column.sort - 3].isExcludeTemp"></div>
                <div class="title">方案{{ column.sort - 2 }}</div>
                <!-- <a-tag style="float: right" color="#2db7f5">{{ getSchemeName(column) }}</a-tag> -->

                <!-- <div class="money">¥15,765.00</div> -->
                <a-button
                  v-if="configCheck('SCHEME_CONFIRM', 'schemeConfirmSelectedTypeConfigDefine') == '1'"
                  size="small"
                  :type="schemeSum[column.sort - 3].isSelected ? 'Default' : 'primary'"
                  shape="round"
                  @click="excludeScheme(column, 'isSelected')"
                  >{{ !schemeSum[column.sort - 3].isSelected ? '选择' : '取消' }}</a-button
                >
                <a-button
                  v-else
                  class="fixed1-btn"
                  size="small"
                  :type="!schemeSum[column.sort - 3].isExcludeTemp ? 'primary' : 'Default'"
                  shape="round"
                  @click="excludeScheme(column, 'isExcludeTemp')"
                  >{{ schemeSum[column.sort - 3].isExcludeTemp ? '取消' : '排除' }}</a-button
                >
              </div>
              <div class="merchant">
                <div
                  class="row"
                  v-for="(item, index) in schemeSum[column.sort - 3].schemeDetails"
                  :key="item.id"
                  v-show="index == 0"
                >
                  <div class="merchant-name">{{ item.pdmMerchantPoolName }}</div>
                  <div class="top-title">{{ item?.title }}</div>

                  <div
                    class="more"
                    v-if="schemeSum[column.sort - 3].schemeDetails.length > 1"
                    v-show="index === 0 ? true : false"
                  >
                    <a-popover trigger="click">
                      <template #content>
                        <div class="merchant" style="margin-top: 0px">
                          <div
                            class="row1"
                            v-for="(item, index) in schemeSum[column.sort - 3].schemeDetails"
                            :key="item.id"
                          >
                            <div class="merchant-name">{{ item.pdmMerchantPoolName }}</div>
                            <div class="top-title">{{ item?.title }}</div>
                            <div class="pool">
                              <div
                                class="poot-btn"
                                v-for="subItem in computedMethod(item.pdmMerchantPoolItems)"
                                :key="subItem"
                              >
                                {{ MiceItemConstant.ofType(subItem).desc }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                      {{ column.showMore ? '收起' : '更多' }}
                    </a-popover>
                  </div>
                </div>
              </div>
              <div class="scheme-details flex">
                <a-tooltip
                  v-if="differentList[column.sort - 3].length > 0"
                  color="#f50"
                  :overlayInnerStyle="{ width: '300px' }"
                >
                  <template #title>
                    <div>
                      <p v-for="text in differentList[column.sort - 3]" :key="text">{{ text }}</p>
                    </div>
                  </template>
                  <div class="details-different">
                    <div><img src="@/assets/image/scheme/LikeOutline.png" width="16" alt="" /></div>
                    <div class="differ-name">
                      差异<span class="count">({{ differentList[column.sort - 3].length }}) </span>
                    </div>
                    <div><img src="@/assets/image/scheme/triangle.png" width="12" alt="" /></div>
                  </div>
                </a-tooltip>
                <div v-else class="details-different">
                  <div><img src="@/assets/image/scheme/LikeOutline.png" width="16" alt="" /></div>
                  <div class="differ-name">
                    差异<span class="count">({{ differentList[column.sort - 3].length }}) </span>
                  </div>
                  <div><img src="@/assets/image/scheme/triangle.png" width="12" alt="" /></div>
                </div>
                <a-divider type="vertical" style="height: 18px" />
                <a-tooltip color="#52c41a" v-if="advantageList[column.sort - 3].length > 0">
                  <template #title>
                    <div>
                      <p v-for="text in advantageList[column.sort - 3]" :key="text">{{ text }}</p>
                    </div>
                  </template>
                  <div class="details-advantage">
                    <div><img src="@/assets/image/scheme/advantage.png" width="16" alt="" /></div>
                    <div class="differ-name">
                      优势<span class="count">({{ advantageList[column.sort - 3].length }}) </span>
                    </div>
                    <div><img src="@/assets/image/scheme/triangle.png" width="12" alt="" /></div>
                  </div>
                </a-tooltip>
                <div v-else class="details-advantage">
                  <div><img src="@/assets/image/scheme/advantage.png" width="16" alt="" /></div>
                  <div class="differ-name">
                    优势<span class="count">({{ advantageList[column.sort - 3].length }}) </span>
                  </div>
                  <div><img src="@/assets/image/scheme/triangle.png" width="12" alt="" /></div>
                </div>
              </div>
            </div>
            <!-- 管理-方案确认,竞价 管理，用户-方案复审 -->
            <div
              class="scheme-item"
              id="my-element"
              v-else-if="
                column.key === 'scheme' &&
                route.path === '/bidman/scheme/confirm' &&
                ((miceDetail.processNode === 'SCHEME_CONFIRM' && $attrs.orderSource === 'manage') ||
                  miceDetail.processNode === 'SCHEME_RE_APPROVAL' ||
                  (['SCHEME_APPROVAL', 'BID_PUSH', 'BIDDING', 'BID_RESULT_CONFIRM']
                    .concat(arrConfirmView)
                    .includes(miceDetail.processNode) &&
                    resolveParam(route.query.record).orderType === 'detail'))
              "
            >
              <div class="item-title">
                <div class="title">方案{{ column.sort - 2 }}</div>
                <div style="display: inline-block; margin-left: 5px" v-if="getSchemeName(column).length > 0">
                  <a-tooltip v-for="tag in getSchemeName(column)" :key="tag.name">
                    <template #title>{{ tag.detail }}</template>
                    <a-tag :color="tag.color">{{ tag.name }}</a-tag>
                  </a-tooltip>
                </div>

                <!-- <div class="money">¥15,765.00</div> -->
              </div>
              <div class="merchant">
                <div
                  class="row"
                  v-for="(item, index) in schemeSum[column.sort - 3].schemeDetails"
                  :key="item.id"
                  v-show="index == 0"
                >
                  <div class="merchant-name">{{ item.pdmMerchantPoolName }}</div>
                  <div class="top-title">{{ item?.title }}</div>

                  <div
                    class="more"
                    v-if="schemeSum[column.sort - 3].schemeDetails.length > 1"
                    v-show="index === 0 ? true : false"
                  >
                    <a-popover trigger="click">
                      <template #content>
                        <div class="merchant" style="margin-top: 0px">
                          <div
                            class="row1"
                            v-for="(item, index) in schemeSum[column.sort - 3].schemeDetails"
                            :key="item.id"
                          >
                            <div class="merchant-name">{{ item.pdmMerchantPoolName }}</div>
                            <div class="top-title">{{ item?.title }}</div>
                            <div class="pool">
                              <div
                                class="poot-btn"
                                v-for="subItem in computedMethod(item.pdmMerchantPoolItems)"
                                :key="subItem"
                              >
                                {{ MiceItemConstant.ofType(subItem).desc }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                      {{ column.showMore ? '收起' : '更多' }}
                    </a-popover>
                  </div>
                </div>
              </div>
              <div class="scheme-details flex">
                <a-tooltip
                  v-if="differentList[column.sort - 3].length > 0"
                  color="#f50"
                  :overlayInnerStyle="{ width: '300px' }"
                >
                  <template #title>
                    <div>
                      <p v-for="text in differentList[column.sort - 3]" :key="text">{{ text }}</p>
                    </div>
                  </template>
                  <div class="details-different">
                    <div><img src="@/assets/image/scheme/LikeOutline.png" width="16" alt="" /></div>
                    <div class="differ-name">
                      差异<span class="count">({{ differentList[column.sort - 3].length }}) </span>
                    </div>
                    <div><img src="@/assets/image/scheme/triangle.png" width="12" alt="" /></div>
                  </div>
                </a-tooltip>
                <div v-else class="details-different">
                  <div><img src="@/assets/image/scheme/LikeOutline.png" width="16" alt="" /></div>
                  <div class="differ-name">
                    差异<span class="count">({{ differentList[column.sort - 3].length }}) </span>
                  </div>
                  <div><img src="@/assets/image/scheme/triangle.png" width="12" alt="" /></div>
                </div>
                <a-divider type="vertical" style="height: 18px" />
                <a-tooltip color="#52c41a" v-if="advantageList[column.sort - 3].length > 0">
                  <template #title>
                    <div>
                      <p v-for="text in advantageList[column.sort - 3]" :key="text">{{ text }}</p>
                    </div>
                  </template>
                  <div class="details-advantage">
                    <div><img src="@/assets/image/scheme/advantage.png" width="16" alt="" /></div>
                    <div class="differ-name">
                      优势<span class="count">({{ advantageList[column.sort - 3].length }}) </span>
                    </div>
                    <div><img src="@/assets/image/scheme/triangle.png" width="12" alt="" /></div>
                  </div>
                </a-tooltip>
                <div v-else class="details-advantage">
                  <div><img src="@/assets/image/scheme/advantage.png" width="16" alt="" /></div>
                  <div class="differ-name">
                    优势<span class="count">({{ advantageList[column.sort - 3].length }}) </span>
                  </div>
                  <div><img src="@/assets/image/scheme/triangle.png" width="12" alt="" /></div>
                </div>
              </div>
            </div>
            <!-- 竞价推送 -->
            <div
              class="scheme-item"
              id="my-element"
              v-else-if="column.key === 'scheme' && route.path === '/bidman/bid/index'"
            >
              <div class="item-title">
                <div class="title">方案{{ column.sort - 2 }}</div>
                <div style="display: inline-block; margin-left: 5px" v-if="getSchemeName(column).length > 0">
                  <a-tooltip v-for="tag in getSchemeName(column)" :key="tag.name">
                    <template #title>{{ tag.detail }}</template>
                    <a-tag :color="tag.color">{{ tag.name }}</a-tag>
                  </a-tooltip>
                </div>
                <!-- <div class="money">¥15,765.00</div> -->
              </div>
              <div class="merchant">
                <div
                  class="row"
                  v-for="(item, index) in schemeSum[column.sort - 3].schemeDetails"
                  :key="item.id"
                  v-show="index == 0"
                >
                  <div class="merchant-name">{{ item.pdmMerchantPoolName }}</div>
                  <div class="top-title">{{ item?.title }}</div>

                  <div
                    class="more"
                    v-if="schemeSum[column.sort - 3].schemeDetails.length > 1"
                    v-show="index === 0 ? true : false"
                  >
                    <a-popover trigger="click">
                      <template #content>
                        <div class="merchant" style="margin-top: 0px">
                          <div
                            class="row1"
                            v-for="(item, index) in schemeSum[column.sort - 3].schemeDetails"
                            :key="item.id"
                          >
                            <div class="merchant-name">{{ item.pdmMerchantPoolName }}</div>
                            <div class="top-title">{{ item?.title }}</div>
                            <div class="pool">
                              <div
                                class="poot-btn"
                                v-for="subItem in computedMethod(item.pdmMerchantPoolItems)"
                                :key="subItem"
                              >
                                {{ MiceItemConstant.ofType(subItem).desc }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                      {{ column.showMore ? '收起' : '更多' }}
                    </a-popover>
                  </div>
                </div>
              </div>
              <div class="scheme-details flex">
                <a-tooltip
                  v-if="differentList[column.sort - 3].length > 0"
                  color="#f50"
                  :overlayInnerStyle="{ width: '300px' }"
                >
                  <template #title>
                    <div>
                      <p v-for="text in differentList[column.sort - 3]" :key="text">{{ text }}</p>
                    </div>
                  </template>
                  <div class="details-different">
                    <div><img src="@/assets/image/scheme/LikeOutline.png" width="16" alt="" /></div>
                    <div class="differ-name">
                      差异<span class="count">({{ differentList[column.sort - 3].length }}) </span>
                    </div>
                    <div><img src="@/assets/image/scheme/triangle.png" width="12" alt="" /></div>
                  </div>
                </a-tooltip>
                <div v-else class="details-different">
                  <div><img src="@/assets/image/scheme/LikeOutline.png" width="16" alt="" /></div>
                  <div class="differ-name">
                    差异<span class="count">({{ differentList[column.sort - 3].length }}) </span>
                  </div>
                  <div><img src="@/assets/image/scheme/triangle.png" width="12" alt="" /></div>
                </div>
                <a-divider type="vertical" style="height: 18px" />
                <a-tooltip color="#52c41a" v-if="advantageList[column.sort - 3].length > 0">
                  <template #title>
                    <div>
                      <p v-for="text in advantageList[column.sort - 3]" :key="text">{{ text }}</p>
                    </div>
                  </template>
                  <div class="details-advantage">
                    <div><img src="@/assets/image/scheme/advantage.png" width="16" alt="" /></div>
                    <div class="differ-name">
                      优势<span class="count">({{ advantageList[column.sort - 3].length }}) </span>
                    </div>
                    <div><img src="@/assets/image/scheme/triangle.png" width="12" alt="" /></div>
                  </div>
                </a-tooltip>
                <div v-else class="details-advantage">
                  <div><img src="@/assets/image/scheme/advantage.png" width="16" alt="" /></div>
                  <div class="differ-name">
                    优势<span class="count">({{ advantageList[column.sort - 3].length }}) </span>
                  </div>
                  <div><img src="@/assets/image/scheme/triangle.png" width="12" alt="" /></div>
                </div>
              </div>
            </div>
          </template>

          <template #bodyCell="{ text, record, index, column }">
            <div
              v-if="Array.isArray(record.demand) && column.key === 'demand' && !record.key.includes('sum')"
              class="demandList"
            >
              <div v-if="record.demand.length > 0" class="demandItem" v-for="item in record.demand" :key="item.value">
                <span class="name">{{ item.name }}：</span>
                <span class="value" v-if="item.name == '附件'" v-html="item.value"></span>
                <span class="value" v-else>{{ item.value }}</span>
              </div>

              <div v-else class="demandList">/</div>
            </div>
            <div v-else-if="column.key === 'leftTitle'" class="leftTitle">
              {{ record[column.dataIndex] }}
            </div>
            <div v-else-if="record.key.includes('sum') && !['合计'].includes(record.subject)">
              <div v-for="(item, index) in record[column.dataIndex]" :key="index" class="flex">
                <div class="vertical" v-if="!(record.subject == '全单服务费' && index == 0)">
                  <a-tooltip>
                    <template #title>{{ item.hotelName == '：' ? '' : item.hotelName }} </template>
                    <span :style="record.subject == '全单服务费' ? 'overflow:visible;text-overflow:inherit' : ''"
                      >{{ item.hotelName == '：' ? '' : item.hotelName }}
                    </span>
                  </a-tooltip>
                </div>
                <div class="vertical1">
                  <div v-for="(subItem, subIndex) in item.list" :key="subIndex">
                    <span>{{ showSumItem(subItem) }}</span>
                  </div>
                </div>
              </div>
              <!-- <div v-else class="">
                <div class="normal-cell" v-for="(item, index) in record[column.dataIndex]" :key="index">
                  <span v-for="(subItem, subIndex) in item" :key="subIndex">{{
                    showMoney
                      ? subItem
                      : subItem?.includes('元*')
                      ? ''
                      : subItem?.includes('元')
                      ? ''
                      : subItem?.replaceAll('=', '')
                  }}</span>
                </div>
              </div> -->
            </div>
            <div v-else-if="column.key === 'secondTitle'" class="secondTitle">
              {{ record[column.dataIndex] }}
            </div>
            <div v-else-if="record.type === 'firstTitle'" class="firstTitle">
              {{ record[column.dataIndex] }}
            </div>

            <div
              v-else
              :class="'normal-cell ' + differentCell(text, record, index, column)"
              v-html="record[column.dataIndex]"
            ></div>
          </template>
        </a-table>
      </div>
      <a-empty v-else />

      <!-- <div class="scheme-sum">
        <div class="sum-title">合计</div>
        <div class="sum-table">
          <a-table :columns="columnsSum" :data-source="dataSum" :pagination="false" :scroll="{ x: '100%' }">
            <template #bodyCell="{ text, record, index, column }">
              <div class="" v-if="!(['subject'].includes(column.key) || record.subject === '合计')">
                <div v-for="(item, index) in record[column.key]" :key="index">
                  <span v-for="(subItem, subIndex) in item" :key="subIndex">{{
                    showMoney
                      ? subItem
                      : subItem.includes('元*')
                      ? ''
                      : subItem.includes('元')
                      ? ''
                      : subItem.replaceAll('=', '')
                  }}</span>
                </div>
              </div>
            </template>
          </a-table>

        </div>
      </div> -->
      <div :class="['scheme-footer ', hideBtn === '1' || $attrs.orderSource === 'user' ? 'footer-user-width' : '']">
        <slot name="footer"></slot>
      </div>
      <a-modal title="排除原因" v-model:open="open" @ok="handleOk">
        <p>是否排除该方案？如果任意会议中相同服务商方案为0，则取消该服务商的全部方案。</p>
        <a-textarea
          v-model:value="schemeSum[currentSchemeNum].excludeRemarks"
          :rows="4"
          :maxLength="200"
          placeholder="请输入排除原因"
        />
      </a-modal>
      <a-modal title="排除原因" v-model:open="openExcludeTemp" @ok="handleOkTemp">
        <p>是否排除该方案？如果任意会议中相同服务商方案为0，则取消该服务商的全部方案。</p>
        <a-textarea
          v-model:value="schemeSum[currentSchemeNum].unselectRemarks"
          :rows="4"
          :maxLength="200"
          placeholder="请输入排除原因"
        />
      </a-modal>
      <a-modal
        title="是否确认推送服务商进行竞价？"
        :confirmLoading="loading"
        v-model:open="showBidPush"
        @ok="handlePush"
      >
        <a-spin :spinning="loading">
          <span style="margin-right: 20px">竞价截止时间：</span>
          <a-date-picker
            v-model:value="publishDate"
            @change="getCurrentHours"
            format="YYYY-MM-DD"
            :disabled-date="disabledDate"
          />
          <a-select v-model:value="publishHour" style="margin-left: 10px; width: 100px">
            <a-select-option
              v-for="hour in hours"
              :disabled="
                dayjs(publishDate).format('YYYY-MM-DD') == dayjs(currentPublishDate).format('YYYY-MM-DD')
                  ? hour <= currentHour
                  : false
              "
              :key="hour"
              :value="hour"
              >{{ hour + ':00' }}</a-select-option
            >
          </a-select>
          <div v-if="isPublishTimeChanged" style="margin-top: 20px">
            <div style="margin-bottom: 10px; color: #ff4d4f; font-size: 14px">
              <QuestionCircleOutlined style="margin-right: 5px" />
              由于修改了截止时间，请上传证明材料
            </div>
            <a-upload
              :before-upload="beforeUpload"
              v-model:fileList="attachmentList"
              :custom-request="uploadRequest"
              @remove="handleRemove"
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
              :multiple="true"
              :max-count="10"
              :showUploadList="{ showRemoveIcon: true }"
            >
              <a-button type="primary">
                <UploadOutlined />
                上传证明材料
              </a-button>
            </a-upload>
          </div>
          <div v-if="isPublishSchemeChanged" style="margin-top: 20px">
            <div style="margin-bottom: 10px; color: #ff4d4f; font-size: 14px">
              <QuestionCircleOutlined style="margin-right: 5px" />
              由于修改了竞价推送策略，请上传证明材料
            </div>
            <a-upload
              :before-upload="beforeUpload"
              v-model:fileList="attachmentListPush"
              :custom-request="uploadRequest"
              @remove="handleRemovePush"
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
              :multiple="true"
              :max-count="10"
              :showUploadList="{ showRemoveIcon: true }"
            >
              <a-button type="primary">
                <UploadOutlined />
                上传证明材料
              </a-button>
            </a-upload>
          </div>
          <a-table style="margin-top: 10px" :columns="bidColumn" :data-source="bidData" :pagination="false" bordered>
            <template #bodyCell="{ text, record, index, column }">
              <div v-if="column.dataIndex === 'scheme' && record.scheme !== '/'">
                <a-select ref="select" v-model:value="record.scheme" style="width: 120px">
                  <a-select-option
                    @change=""
                    v-for="item in MiceSchemePushStrategy.toArray()"
                    :key="item.code"
                    :value="item.code"
                    >{{ item.desc }}</a-select-option
                  >
                </a-select>
              </div>
              <div v-else>{{ record[column.dataIndex] }}</div>
            </template>
          </a-table>
        </a-spin>
      </a-modal>
      <a-modal
        v-model:open="approvalModalShow"
        title="已提交如下人员审批"
        width="80%"
        v-if="approvalModalShow"
        :keyboard="false"
        :maskClosable="false"
        :closable="false"
      >
        <div>
          <iframe
            width="100%"
            :src="businessProcess + '?code=' + approveCode + '#/detailsPcSt'"
            frameborder="0"
          ></iframe>
        </div>
        <template #footer>
          <a-button
            @click="
              approvalModalShow = false;
              router.push({ path: '/bidman/orderList/index', query: { status: '0' } });
              isCloseLastTab = true;
            "
            >确定</a-button
          >
        </template>
      </a-modal>
      <a-modal title="竞价列表" width="60%" v-model:open="showBidPushed" :footer="null">
        <a-table style="margin-top: 10px" :columns="bidedColumn" :data-source="bidedData" :pagination="false" bordered>
          <template #bodyCell="{ text, record, index, column }">
            <div v-if="column.dataIndex === 'scheme'">
              <div v-for="item in record.scheme">{{ item.bidMerchantName }}</div>
            </div>
            <div v-else>{{ record[column.dataIndex] }}</div>
          </template>
        </a-table>
        <div class="footer">
          <a-button
            type="primary"
            @click="
              () => {
                router.push({ path: '/bidman/orderList/index', query: { status: '0' } });
                showBidPushed = false;
                isCloseLastTab = true;
              }
            "
            >确定</a-button
          >
        </div>
      </a-modal>
    </a-spin>
  </div>
</template>

<style lang="less" scoped>
.scheme-container {
  position: relative;
  width: 100%;
  padding-bottom: 50px;
}
// .scheme-header {
//   vertical-align: middle;
//   width: 100%;
//   height: 192px;
//   padding: 24px 32px;

//   background: url('@/assets/image/scheme/mice_bgc.png');
//   background-repeat: no-repeat;
//   background-size: 100% 100%;
// }
.scheme-title {
  display: inline-block;
  font-family: PingFangSCSemibold, PingFangSCSemibold;
  font-weight: normal;
  font-size: 20px;
  color: #1d2129;
  line-height: 28px;
  text-align: left;
  font-style: normal;
  vertical-align: middle;

  text-indent: 36px;
  background-image: url('@/assets/image/scheme/mice_name.png');
  background-size: 28px 28px;
  background-repeat: no-repeat;
}
.scheme-node {
  vertical-align: middle;
  margin-left: 24px;
  display: inline-block;
  width: 108px;
  height: 28px;
  // background: #faad14;
  border-radius: 4px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #ffffff;
  line-height: 28px;
  text-align: center;
  font-style: normal;
}
.scheme-code {
  margin-top: 12px;
  display: inline-block;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #86909c;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  img {
    cursor: pointer;
  }
}
.header-details {
  margin-top: 32px;
  .details-item {
    // white-space: nowrap;
    display: inline-block;
    width: 33%;
  }
  .label {
    display: inline-block;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #86909c;
    line-height: 20px;
    text-align: left;
    font-style: normal;

    text-indent: 26px;
    background-size: 16px 16px;
    background-repeat: no-repeat;
    background-position: center left;
    background-repeat: no-repeat;
  }
  .mice_info_person_img {
    background-image: url('@/assets/image/scheme/mice_person.png');
  }

  .mice_info_type_img {
    background-image: url('@/assets/image/scheme/mice_type.png');
  }

  .mice_info_price_img {
    background-image: url('@/assets/image/scheme/mice_price.png');
  }

  .mice_info_time_img {
    background-image: url('@/assets/image/scheme/mice_time.png');
  }
}
.scheme-tip {
  padding: 0 16px;
  background: #e8f0fc;
  border-radius: 4px;
  height: 36px;
  margin: 20px 24px;
  border: 1px solid #1868db;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #1d2129;
  line-height: 40px;
  text-align: left;
  font-style: normal;
  .result {
    cursor: pointer;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 14px;
    color: #1868db;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    text-decoration-line: underline;
  }

  img {
    margin-right: 8px;
  }
}
img {
  vertical-align: -15%;
}
.scheme-different {
  text-align: center;
}
.flex {
  display: flex;
  justify-content: space-around;
}
.vertical {
  width: 35%;
  display: flex;
  align-items: center;
  justify-content: space-around;
  span {
    // text-align: right;
    width: 80%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.vertical1 {
  width: 65%;
  display: flex;
  align-items: center;
  flex-direction: column;
  div {
    width: 100%;
  }
  span {
    text-align: left;
    overflow: hidden;
    // white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.merchant {
  background: #f2f7ff;
  margin-top: 16px;
  // min-width: 200px;
  padding: 11px;
  .row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
  }
  .row {
    margin-bottom: 8px;
  }
  .merchant-name {
    white-space: nowrap;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #4e5969;
    line-height: 20px;
    text-align: left;
    font-style: normal;
  }
  .more {
    cursor: pointer;
    margin-left: 4px;
    display: inline-block;
    color: #1868db;
    white-space: nowrap;
    font-size: 12px;
  }
  .pool {
    // overflow: hidden;
    // white-space: nowrap;
    // text-overflow: ellipsis;
    // float: right;
    margin-bottom: 12px;
    max-width: 200px;

    .poot-btn {
      margin-top: 5px;
      display: inline-block;
      padding: 0 4px;
      height: 22px;
      background: rgba(255, 255, 255, 0);
      border-radius: 4px;
      border: 1px solid rgba(24, 104, 219, 0.6);
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: #1868db;
      line-height: 20px;
      text-align: right;
      font-style: normal;
      margin-left: 4px;
    }
  }
}
.scheme-item {
  position: relative;
  // min-width: 320px;
  padding: 8px;
  text-align: justify;
  .exclude-icon {
    position: absolute;
    top: -2px;
    left: -2px;
    width: 51px;
    height: 51px;
    background: url('@/assets/image/scheme/exclude.png') no-repeat;
    background-size: 100% 100%;
  }
  .item-title {
    white-space: nowrap;
    padding: 0 8px;
  }

  .title {
    display: inline-block;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #1d2129;
    line-height: 22px;
    text-align: left;
    font-style: normal;
  }
  .money {
    margin-left: 12px;
    display: inline-block;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #1868db;
    line-height: 22px;
    text-align: left;
    font-style: normal;
  }
  :deep(.ant-btn-round) {
    display: inline-block;
    // background: #1868db;
    float: right;
  }
}
.hotel {
  width: 100%;
  :deep(.ant-table-cell) {
    padding: 0 !important;
  }
  .normal-cell {
    padding: 10px 12px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #1d2129;
    line-height: 20px;
    text-align: left;
    font-style: normal;
  }
}
.scheme-details {
  padding: 12px 20px;
  cursor: pointer;
}
.differ-name {
  margin: auto;
  white-space: nowrap;
  // margin: 0 16px 0 8px;
  display: inline-block;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #1d2129;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  .count {
    color: #ff5533;
  }
}
.details-advantage {
  width: 40%;
  display: flex;
  justify-content: space-around;
  white-space: nowrap;
  .count {
    color: #52c41a;
  }
}
.details-different {
  width: 40%;
  display: flex;
  justify-content: space-around;
  white-space: nowrap;
}
.scheme-footer {
  z-index: 9;
  position: fixed;
  bottom: 0;
  right: 0;
  padding: 10px 20px;
  margin-top: 10px;
  width: calc(100% - 220px);
  // text-align: left;
  background: #ffffff;
  border-top: 1px solid #f1f2f6;
  box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.03);
}
.wid100 {
  width: 100%;
}
.footer-user-width {
  width: 1280px !important;
  left: calc(50% - 640px);
}
.demandItem {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #86909c;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  // white-space: nowrap;
  margin: 5px 0;
  padding: 0 12px;
  .name {
    // display: inline-block;
    color: #86909c;
  }
  .value {
    // display: inline-block;
    color: #1d2129;
  }
}
.secondTitle {
  white-space: nowrap;
  padding: 0 12px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #86909c;
  line-height: 20px;
  text-align: right;
  font-style: normal;
}
.leftTitle {
  display: inline-block;
  width: 100%;
  // background: #f7f8fa;
  padding: 10px 12px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #4e5969;
  line-height: 20px;
  text-align: center;
  font-style: normal;
}
.firstTitle {
  background: #f7f8fa;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #262626;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  padding: 10px 12px;
}
// :deep(.ant-table-row-level-0) {
//   .ant-table-cell:first-of-type {
//     background: #f7f8fa;
//   }
// }
.scheme-sum {
  background: #fff;
  margin-top: 24px;
  width: 100%;
  // height: 400px;
}

.sum-table {
  padding: 16px 24px;
  width: 100%;
  .sumData {
    text-align: right;
    margin-top: 17px;
    width: 100%;
    background: #1868db;
    height: 44px;
    border-radius: 4px;
    padding: 8px 14px;
    .label {
      display: inline-block;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #f7f8fc;
      line-height: 0px;
      text-align: left;
      font-style: normal;
      vertical-align: middle;
    }
    .value {
      display: inline-block;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 20px;
      color: #f7f8fc;
      line-height: 28px;
      text-align: right;
      font-style: normal;
      vertical-align: middle;
    }
  }
  :deep(.ant-table-cell) {
    padding: 7px 16px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #1d2129;
    line-height: 22px;
    text-align: left;
    font-style: normal;
  }
  :deep(.ant-table-thead) {
    background: #f7f8fa;

    .ant-table-cell {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #1d2129;
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
  }
}
.sum-title {
  display: inline-block;
  padding-left: 20px;
  height: 20px;
  line-height: 20px;
  margin-top: 24px;
  border-left: 4px solid #1868db;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 18px;
  color: #1d2129;
  text-align: left;
  font-style: normal;
}
.different-cell {
  color: #ff5533 !important;
}
:deep(.ant-btn-Default) {
  background: rgba(24, 104, 219, 0.1) !important;
  color: #1868db;
}
.footer {
  margin-top: 20px;
  text-align: center;
}
.fixed-btn {
  margin-left: 120px;
  position: fixed;
  z-index: 999;
}
// 固定第一行
// :deep(.ant-table-tbody > ant-table-cell:first-child) {
//   position: fixed !important;
//   top: 0 !important;
//   z-index: 1 !important;
// }
:deep(.btn-tooltip) {
  float: right;
}
.tooltip-top {
  vertical-align: -34%;
  display: inline-block;
}
.tooltip-compare {
  margin: 0 100px;
  display: flex;
  justify-content: space-between;
}
</style>
