<script setup lang="ts">
import { message } from 'ant-design-vue';
import { ref } from 'vue';
import { schemeApi } from '@haierbusiness-front/apis';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';

// Props 接收父组件传递的数据验证函数
const props = defineProps({
  // 验证所有数据是否完整的函数
  validateAllData: {
    type: Function,
    required: true,
  },
  // 获取导出数据的函数
  getExportData: {
    type: Function,
    required: true,
  },
  // 当前方案类型
  schemeType: {
    type: String,
    default: 'billUpload',
  },
});

const exportLoading = ref(false);
const importLoading = ref(false);

// 数据完整性验证
const validateDataIntegrity = async (): Promise<boolean> => {
  try {
    // 调用父组件的验证函数，复用完成提报的校验逻辑
    const isValid = await props.validateAllData();

    if (!isValid) {
      message.error('数据验证失败，请检查必填项是否完整！');
      return false;
    }

    return true;
  } catch (error) {
    console.error('数据验证过程中出现错误:', error);
    message.error('数据验证失败，请重试！');
    return false;
  }
};

// 导出功能
const handleExport = async () => {
  exportLoading.value = true;

  try {
    console.log('开始费用确认导出...');

    // 第一步：数据完整性验证
    const isDataValid = await validateDataIntegrity();
    if (!isDataValid) {
      return;
    }

    // 第二步：获取导出数据
    const exportData = await props.getExportData();
    if (!exportData) {
      message.error('获取导出数据失败！');
      return;
    }

    console.log('导出数据:', exportData);

    // 第三步：调用导出接口
    const response = await schemeApi.exportExpenseConfirmation(exportData);
    
    // 处理文件下载
    if (response && response.data) {
      // 如果返回的是文件流，则触发下载
      const blob = new Blob([response.data], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `费用确认明细_${new Date().getTime()}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    }

    message.success('费用确认导出成功！');
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败，请重试！');
  } finally {
    exportLoading.value = false;
  }
};

// 导入功能
const handleImport = async () => {
  importLoading.value = true;

  try {
    console.log('开始费用确认导入...');

    // TODO: 实现文件选择和导入逻辑
    // 1. 文件选择
    // 2. 文件格式验证
    // 3. 数据解析
    // 4. 数据导入

    message.success('费用确认导入成功！');
  } catch (error) {
    console.error('导入失败:', error);
    message.error('导入失败，请重试！');
  } finally {
    importLoading.value = false;
  }
};
</script>

<template>
  <div class="export-expense-confirmation">
    <div class="contract_title">
      <div class="interact_shu mr20"></div>
      <span>结算单上传</span>
      <a-tooltip class="ml10">
        <template #title>
          导出结算单后，线下盖章，将盖完章的结算单重新导入
        </template>
        <QuestionCircleOutlined />
      </a-tooltip>
    </div>
    <div class="contract_wrapper">
      <div class="export-item">
        <div class="button-group">
          <a-button :loading="exportLoading" @click="handleExport">导出结算单</a-button>
          <a-button :loading="importLoading" @click="handleImport">导入结算单</a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
*{
  font-family: PingFangSC, PingFang SC;
}
.export-expense-confirmation {
  padding-top: 24px;

  .interact_shu {
    width: 4px;
    height: 20px;
    background: #1868db;
    border-radius: 2px;
  }

  .contract_title {
    margin-bottom: 20px;
    font-size: 18px;
    color: #333;
    // font-weight: 500;
    display: flex;
    align-items: center;
    // gap: 8px;

    span {
      font-size: 18px;
      // font-weight: 500;
      color: #1d2129;
    }
  }

  .contract_wrapper {
    border-radius: 6px;
    overflow: hidden;
    width: 70%;

    .export-item {
      display: flex;
      align-items: center;
      padding: 18px 30px;
      padding-top: 0px;
      background-color: #fff;
      transition: background-color 0.3s;

      // &:hover {
      //   background-color: #fafafa;
      // }

      .export-label {
        width: 140px;
        font-size: 14px;
        color: #333;
        margin-right: 40px;
        flex-shrink: 0;
        font-weight: 500;
      }

      .button-group {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 24px;

        :deep(.ant-btn) {
          height: 36px;
          font-size: 14px;
          padding: 0 20px;
          border-radius: 4px;

          &[type='primary'] {
            background-color: #1890ff;
            border-color: #1890ff;

            &:hover {
              background-color: #40a9ff;
              border-color: #40a9ff;
            }
          }

          &:not([type='primary']) {
            border-color: #d9d9d9;
            color: #666;

            &:hover {
              border-color: #1890ff;
              color: #1890ff;
            }
          }
        }
      }
    }
  }
}

.mr20 {
  margin-right: 20px;
}

.ml10 {
  margin-left: 10px;
}
</style>
